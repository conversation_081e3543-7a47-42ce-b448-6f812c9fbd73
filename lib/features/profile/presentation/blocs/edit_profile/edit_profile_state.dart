part of 'edit_profile_cubit.dart';

abstract class <PERSON>ProfileState extends Equatable {
  const EditProfileState();

  @override
  List<Object?> get props => [];
}

class EditProfileInitial extends EditProfileState {}

class EditProfileLoading extends Edit<PERSON><PERSON>fileState {}

class EditProfileLoaded extends EditProfileState {
  final ProfileResponse profileResponse;

  const EditProfileLoaded(this.profileResponse);

  @override
  List<Object?> get props => [profileResponse];
}

class EditProfileUpdating extends EditProfileState {}

class EditProfileUpdateSuccess extends EditProfileState {
  final ProfileResponse profileResponse;

  const EditProfileUpdateSuccess(this.profileResponse);

  @override
  List<Object?> get props => [profileResponse];
}

class EditProfileError extends EditProfileState {
  final String message;

  const EditProfileError(this.message);

  @override
  List<Object?> get props => [message];
}
