import '../../../../shared/models/result.dart';
import '../../data/models/profile_response.dart';
import '../../data/models/update_profile_request.dart';

abstract class ProfileRepository {
  /// Get Profile Data
  Future<Result<ProfileResponse>> getProfile({
    required String token,
    required String userId,
  });

  /// Update Profile Data
  Future<Result<ProfileResponse>> updateProfile({
    required UpdateProfileRequest request,
  });
}
