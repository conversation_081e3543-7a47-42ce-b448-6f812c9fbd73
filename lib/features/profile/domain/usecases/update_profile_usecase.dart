import '../../../../shared/models/result.dart';
import '../../data/models/profile_response.dart';
import '../../data/models/update_profile_request.dart';
import '../repositories/profile_repository.dart';

class UpdateProfileUseCase {
  final ProfileRepository _repository;

  UpdateProfileUseCase(this._repository);

  Future<Result<ProfileResponse>> call({
    required UpdateProfileRequest request,
  }) async {
    return await _repository.updateProfile(request: request);
  }
}
