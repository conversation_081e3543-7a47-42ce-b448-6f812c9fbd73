import '../../../../core/network/network_info.dart';
import '../../../../shared/models/result.dart';
import '../../domain/repositories/profile_repository.dart';
import '../datasources/profile_remote_datasource.dart';
import '../models/profile_response.dart';
import '../models/update_profile_request.dart';

class ProfileRepositoryImpl implements ProfileRepository {
  final ProfileRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  ProfileRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Result<ProfileResponse>> getProfile({
    required String token,
    required String userId,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.getProfile(
        token: token,
        userId: userId,
      );
    } else {
      return Result.failure(
        'No internet connection. Profile data requires an active connection.',
      );
    }
  }

  @override
  Future<Result<ProfileResponse>> updateProfile({
    required UpdateProfileRequest request,
  }) async {
    if (await networkInfo.isConnected) {
      return await remoteDataSource.updateProfile(request: request);
    } else {
      return Result.failure(
        'No internet connection. Profile update requires an active connection.',
      );
    }
  }
}
