import 'package:dio/dio.dart';
import '../../../../shared/models/result.dart';
import '../models/profile_response.dart';
import '../models/update_profile_request.dart';

abstract class ProfileRemoteDataSource {
  /// Get Profile Data
  Future<Result<ProfileResponse>> getProfile({
    required String token,
    required String userId,
  });

  /// Update Profile Data
  Future<Result<ProfileResponse>> updateProfile({
    required UpdateProfileRequest request,
  });
}

class ProfileRemoteDataSourceImpl implements ProfileRemoteDataSource {
  /// Constructor
  ProfileRemoteDataSourceImpl(this._dio);

  final Dio _dio;

  @override
  Future<Result<ProfileResponse>> getProfile({
    required String token,
    required String userId,
  }) async {
    try {
      final response = await _dio.get(
        'https://webservice2.storetrack.com.au/api/profile_postal',
        queryParameters: {
          'token': token,
          'user_id': userId,
        },
      );

      final profileResponse = ProfileResponse.fromJson(response.data);
      return Result.success(profileResponse);
    } catch (e) {
      return Result.failure(
        'Could not get profile data due to a server or network issue. Please try again.',
      );
    }
  }

  @override
  Future<Result<ProfileResponse>> updateProfile({
    required UpdateProfileRequest request,
  }) async {
    try {
      final response = await _dio.post(
        'https://webservice2.storetrack.com.au/api/profile_postal',
        data: request.toJson(),
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      final profileResponse = ProfileResponse.fromJson(response.data);
      return Result.success(profileResponse);
    } catch (e) {
      return Result.failure(
        'Could not update profile data due to a server or network issue. Please try again.',
      );
    }
  }
}
