import 'dart:convert';

class ProfileResponse {
  Data? data;

  ProfileResponse({
    this.data,
  });

  factory ProfileResponse.fromRawJson(String str) =>
      ProfileResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ProfileResponse.fromJson(Map<String, dynamic> json) =>
      ProfileResponse(
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
      };
}

class Data {
  String? token;
  int? userId;
  int? contractorId;
  int? countryId;
  int? stateId;
  String? firstName;
  String? lastName;
  String? address;
  String? email;
  String? country;
  String? state;
  String? suburb;
  String? postcode;
  String? pAddress;
  String? pSuburb;
  String? pPostcode;
  int? pCountryId;
  String? pCountry;
  String? pRegion;
  int? pRegionId;
  String? pDeliveryComment;
  String? mobile;
  String? profileImageUrl;
  String? modifiedTimeStampProfile;
  bool? adminAccess;
  bool? createTask;
  String? orgIDs;

  Data({
    this.token,
    this.userId,
    this.contractorId,
    this.countryId,
    this.stateId,
    this.firstName,
    this.lastName,
    this.address,
    this.email,
    this.country,
    this.state,
    this.suburb,
    this.postcode,
    this.pAddress,
    this.pSuburb,
    this.pPostcode,
    this.pCountryId,
    this.pCountry,
    this.pRegion,
    this.pRegionId,
    this.pDeliveryComment,
    this.mobile,
    this.profileImageUrl,
    this.modifiedTimeStampProfile,
    this.adminAccess,
    this.createTask,
    this.orgIDs,
  });

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        token: json["token"],
        userId: json["user_id"],
        contractorId: json["contractor_id"],
        countryId: json["country_id"],
        stateId: json["state_id"],
        firstName: json["first_name"],
        lastName: json["last_name"],
        address: json["address"],
        email: json["email"],
        country: json["country"],
        state: json["state"],
        suburb: json["suburb"],
        postcode: json["postcode"],
        pAddress: json["P_Address"],
        pSuburb: json["P_Suburb"],
        pPostcode: json["P_Postcode"],
        pCountryId: json["P_Country_id"],
        pCountry: json["P_Country"],
        pRegion: json["P_Region"],
        pRegionId: json["P_Region_id"],
        pDeliveryComment: json["P_Delivery_Comment"],
        mobile: json["mobile"],
        profileImageUrl: json["profile_image_url"],
        modifiedTimeStampProfile: json["modified_time_stamp_profile"],
        adminAccess: json["admin_access"],
        createTask: json["create_task"],
        orgIDs: json["OrgIDs"],
      );

  Map<String, dynamic> toJson() => {
        "token": token,
        "user_id": userId,
        "contractor_id": contractorId,
        "country_id": countryId,
        "state_id": stateId,
        "first_name": firstName,
        "last_name": lastName,
        "address": address,
        "email": email,
        "country": country,
        "state": state,
        "suburb": suburb,
        "postcode": postcode,
        "P_Address": pAddress,
        "P_Suburb": pSuburb,
        "P_Postcode": pPostcode,
        "P_Country_id": pCountryId,
        "P_Country": pCountry,
        "P_Region": pRegion,
        "P_Region_id": pRegionId,
        "P_Delivery_Comment": pDeliveryComment,
        "mobile": mobile,
        "profile_image_url": profileImageUrl,
        "modified_time_stamp_profile": modifiedTimeStampProfile,
        "admin_access": adminAccess,
        "create_task": createTask,
        "OrgIDs": orgIDs,
      };
}
