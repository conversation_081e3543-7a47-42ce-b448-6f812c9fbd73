import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';
import 'package:storetrack_app/core/database/realm_database.dart';

/// A reusable card widget for displaying question parts with progress indicators
///
/// Features:
/// - Progress bar showing completion status
/// - Mandatory indicator (red exclamation mark)
/// - Consistent styling following app design patterns
/// - Tap navigation to question part measurement details
/// - Clean, readable layout with proper spacing
class SubHeaderCard extends StatelessWidget {
  final entities.QuestionPart questionPart;
  final entities.Question question;
  final bool isMandatory;
  final num? taskId;
  final num? formId;
  final VoidCallback? onTap;

  /// Constructor for SubHeaderItemCard

  const SubHeaderCard({
    super.key,
    required this.questionPart,
    required this.question,
    required this.isMandatory,
    this.taskId,
    this.formId,
    this.onTap,
  });

  /// Calculate progress for this question part based on measurements
  double _calculateProgress() {
    if (question.measurements == null || question.measurements!.isEmpty) {
      return 0.0;
    }

    final totalMeasurements = question.measurements!.length;
    int answeredMeasurements = 0;

    if (taskId != null &&
        formId != null &&
        question.questionId != null &&
        questionPart.questionpartId != null) {
      try {
        final realm = RealmDatabase.instance.realm;

        // Find the task with the matching taskId
        final taskModel = realm.query<TaskDetailModel>(
            'taskId == \$0', [taskId!.toInt()]).firstOrNull;

        if (taskModel != null) {
          // Find the form with the matching formId
          final formModel = taskModel.forms
              .where((form) => form.formId == formId!.toInt())
              .firstOrNull;

          if (formModel != null) {
            // Get saved answers for this specific question part
            final savedAnswers = formModel.questionAnswers
                .where((answer) =>
                    answer.questionId == question.questionId!.toInt() &&
                    answer.questionpartId ==
                        questionPart.questionpartId!.toInt())
                .toList();

            // Count unique measurements that have been answered for this question part
            final answeredMeasurementIds = <int>{};
            for (final answer in savedAnswers) {
              if (answer.measurementId != null) {
                answeredMeasurementIds.add(answer.measurementId!);
              }
            }
            answeredMeasurements = answeredMeasurementIds.length;
          }
        }
      } catch (e) {
        // Error calculating progress - use default values
      }
    }

    return totalMeasurements > 0
        ? answeredMeasurements / totalMeasurements
        : 0.0;
  }

  /// Get progress text showing completed vs total measurements
  String _getProgressText() {
    if (question.measurements == null || question.measurements!.isEmpty) {
      return '0 of 0';
    }

    final totalMeasurements = question.measurements!.length;
    int answeredMeasurements = 0;

    if (taskId != null &&
        formId != null &&
        question.questionId != null &&
        questionPart.questionpartId != null) {
      try {
        final realm = RealmDatabase.instance.realm;

        // Find the task with the matching taskId
        final taskModel = realm.query<TaskDetailModel>(
            'taskId == \$0', [taskId!.toInt()]).firstOrNull;

        if (taskModel != null) {
          // Find the form with the matching formId
          final formModel = taskModel.forms
              .where((form) => form.formId == formId!.toInt())
              .firstOrNull;

          if (formModel != null) {
            // Get saved answers for this specific question part
            final savedAnswers = formModel.questionAnswers
                .where((answer) =>
                    answer.questionId == question.questionId!.toInt() &&
                    answer.questionpartId ==
                        questionPart.questionpartId!.toInt())
                .toList();

            // Count unique measurements that have been answered for this question part
            final answeredMeasurementIds = <int>{};
            for (final answer in savedAnswers) {
              if (answer.measurementId != null) {
                answeredMeasurementIds.add(answer.measurementId!);
              }
            }
            answeredMeasurements = answeredMeasurementIds.length;
          }
        }
      } catch (e) {
        // Error calculating progress - use default values
      }
    }

    return '$answeredMeasurements of $totalMeasurements';
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final progress = _calculateProgress();
    final progressText = _getProgressText();

    return Stack(
      children: [
        Container(
          margin: const EdgeInsets.symmetric(vertical: 4.0),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: InkWell(
            borderRadius: BorderRadius.circular(10),
            onTap: onTap,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title row with mandatory indicator
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          questionPart.questionpartDescription ??
                              'Unnamed Part',
                          style: textTheme.montserratTitleExtraSmall,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (isMandatory) ...[
                        const Gap(8),
                        Container(
                          width: 16,
                          height: 16,
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.priority_high,
                            color: AppColors.loginRed,
                            size: 16,
                          ),
                        ),
                      ],
                    ],
                  ),

                  const Gap(16),

                  // Progress bar
                  Row(
                    children: [
                      Expanded(
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(4),
                          child: LinearProgressIndicator(
                            value: progress,
                            backgroundColor: Colors.grey.shade200,
                            valueColor: const AlwaysStoppedAnimation<Color>(
                                AppColors.primaryBlue),
                            minHeight: 6,
                          ),
                        ),
                      ),
                      const Gap(24),
                      Text(
                        progressText,
                        style: textTheme.montserratTableSmall,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
