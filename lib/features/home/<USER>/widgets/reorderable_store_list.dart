import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:storetrack_app/core/constants/app_assets.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import '../../../../core/utils/snackbar_service.dart';
import '../../../../core/utils/task_grouping.dart';
import '../../../../core/storage/data_manager.dart';
import '../../../../di/service_locator.dart';
import '../constants/action_types.dart';
import '../widgets/task_action_button.dart';
import 'store_card.dart';

class ReorderableStoreList extends StatefulWidget {
  final List<TaskDetail> tasks;
  final bool isCalendarMode;
  final bool showScheduledDate;
  final bool showTickIndicator;
  final bool showAllDisclosureIndicator;
  final bool permanentlyDisableAllDisclosureIndicator;
  final bool isOpenTask;
  final Function(String actionType, TaskDetail task)? onParentActionTap;
  final Function(String actionType, TaskDetail task)? onSubtaskActionTap;
  final Function(List<TaskDetail> selectedItems)? onSelectionChanged;
  final bool selectAll;

  const ReorderableStoreList({
    super.key,
    required this.tasks,
    required this.isCalendarMode,
    this.showScheduledDate = false,
    this.showTickIndicator = false,
    this.showAllDisclosureIndicator = false,
    this.permanentlyDisableAllDisclosureIndicator = false,
    this.isOpenTask = false,
    this.onParentActionTap,
    this.onSubtaskActionTap,
    this.onSelectionChanged,
    this.selectAll = false,
  });

  @override
  State<ReorderableStoreList> createState() => _ReorderableStoreListState();
}

class _ReorderableStoreListState extends State<ReorderableStoreList> {
  late List<TaskDetail> _tasks;
  late List<List<TaskDetail>> _tasksNew;
  late List<Widget> _parentActions;
  late List<Widget> _subtaskActions;
  final Set<String> _selectedTaskIds = {}; // Track selected task IDs
  final Map<String, bool> _checkboxStates =
      {}; // Track checkbox states for all tasks
  late final DataManager _dataManager;

  @override
  void initState() {
    super.initState();
    _dataManager = sl<DataManager>();
    _tasks = List<TaskDetail>.from(widget.tasks);
    // Initialize with default grouping first to avoid LateInitializationError
    _tasksNew = groupTasksByStore(_tasks);
    _buildActionButtons();
    _initializeCheckboxStates();

    // Initialize with selectAll state if needed
    if (widget.selectAll) {
      _doSelectAll(true);
    }

    // Load saved order asynchronously after initial setup
    _loadSavedTaskOrder();
  }

  // Load saved task order asynchronously
  void _loadSavedTaskOrder() async {
    try {
      final savedOrder = await _dataManager.getTaskOrder();
      if (savedOrder != null && savedOrder.isNotEmpty) {
        setState(() {
          _applySavedTaskOrder(savedOrder);
        });
      }
      // If no saved order exists, keep the default grouping already set
    } catch (e) {
      // If there's an error loading saved order, keep the default grouping
      debugPrint('Error loading saved task order: $e');
    }
  }

  // Apply saved task order to the current tasks
  void _applySavedTaskOrder(List<String> savedOrder) {
    // Create a map for quick lookup of tasks by ID
    Map<String, TaskDetail> taskMap = {};
    for (var task in _tasks) {
      taskMap[task.taskId.toString()] = task;
    }

    // Create ordered list based on saved order
    List<TaskDetail> orderedTasks = [];

    // First, add tasks in the saved order
    for (String taskId in savedOrder) {
      if (taskMap.containsKey(taskId)) {
        orderedTasks.add(taskMap[taskId]!);
        taskMap.remove(taskId); // Remove to avoid duplicates
      }
    }

    // Then add any new tasks that weren't in the saved order
    orderedTasks.addAll(taskMap.values);

    // Update the tasks list and regroup
    _tasks = orderedTasks;
    _tasksNew = groupTasksByStore(_tasks);
  }

  // Extract all task IDs in their current order
  List<String> _extractCurrentTaskOrder() {
    return _tasks.map((task) => task.taskId.toString()).toList();
  }

  // Save current task order
  Future<void> _saveCurrentTaskOrder() async {
    try {
      final currentOrder = _extractCurrentTaskOrder();
      await _dataManager.saveTaskOrder(currentOrder);
    } catch (e) {
      // Handle error silently to avoid disrupting user experience
      debugPrint('Error saving task order: $e');
    }
  }

  // Initialize checkbox states for all tasks
  void _initializeCheckboxStates() {
    _checkboxStates.clear();
    for (var taskGroup in _tasksNew) {
      for (var task in taskGroup) {
        _checkboxStates[task.taskId.toString()] = false;
      }
    }
  }

  // Helper method to get all selected tasks
  List<TaskDetail> _getSelectedTasks() {
    return _tasks
        .where((task) => _selectedTaskIds.contains(task.taskId.toString()))
        .toList();
  }

  // Handle selection change and notify parent
  void _handleSelectionChanged() {
    if (widget.onSelectionChanged != null) {
      // Direct callback for user interactions
      widget.onSelectionChanged!(_getSelectedTasks());
    }
  }

  // Select or deselect all tasks - this can be called from props (didUpdateWidget) or user action
  void selectAll(bool selectAll) {
    // For safety, always use post-frame callback when called from didUpdateWidget
    // This ensures we don't call setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _doSelectAll(selectAll);
    });
  }

  // Helper method to perform the actual selection update
  void _doSelectAll(bool selectAll) {
    // We need to update the checkbox states directly without setState here
    // since this might be called during build
    if (selectAll) {
      // Select all tasks
      _selectedTaskIds.clear();
      for (var taskGroup in _tasksNew) {
        for (var task in taskGroup) {
          _selectedTaskIds.add(task.taskId.toString());
          _checkboxStates[task.taskId.toString()] = true;
        }
      }
    } else {
      // Deselect all tasks
      _selectedTaskIds.clear();
      for (var key in _checkboxStates.keys) {
        _checkboxStates[key] = false;
      }
    }

    // We don't notify the parent here since this is coming from the parent
    // and would create an infinite loop.
    // The parent already knows the selection state.
  }

  // Handle parent checkbox change
  void _handleParentCheckboxChanged(bool? value, TaskDetail parentTask) {
    final bool isChecked = value ?? false;
    setState(() {
      // Get all subtasks for this parent
      final List<TaskDetail> subtasks = _tasksNew.firstWhere(
        (group) => group.isNotEmpty && group.first.taskId == parentTask.taskId,
        orElse: () => [],
      );

      // Update checkbox state for parent
      _checkboxStates[parentTask.taskId.toString()] = isChecked;

      // Update selection and checkbox state for all subtasks
      for (var task in subtasks) {
        final taskId = task.taskId.toString();
        // Update selection tracking
        if (isChecked) {
          _selectedTaskIds.add(taskId);
        } else {
          _selectedTaskIds.remove(taskId);
        }
        // Update checkbox state
        _checkboxStates[taskId] = isChecked;
      }
    });

    // Notify parent of selection change
    _handleSelectionChanged();
  }

  // Handle subtask checkbox change
  void _handleSubtaskCheckboxChanged(bool? value, TaskDetail subtask) {
    final bool isChecked = value ?? false;
    final String taskId = subtask.taskId.toString();

    setState(() {
      // Update selection tracking
      if (isChecked) {
        _selectedTaskIds.add(taskId);
      } else {
        _selectedTaskIds.remove(taskId);
      }

      // Update checkbox state
      _checkboxStates[taskId] = isChecked;

      // Find parent task group
      for (var taskGroup in _tasksNew) {
        if (taskGroup.any((task) => task.taskId == subtask.taskId)) {
          // Check if all subtasks are selected to update parent checkbox
          bool allSelected = taskGroup
              .every((task) => _checkboxStates[task.taskId.toString()] == true);

          // Update parent checkbox state if there's at least one task in the group
          if (taskGroup.isNotEmpty) {
            _checkboxStates[taskGroup.first.taskId.toString()] = allSelected;
          }

          break;
        }
      }
    });

    // Notify parent of selection change
    _handleSelectionChanged();
  }

  void _buildActionButtons() {
    if (widget.isCalendarMode) {
      _parentActions = [
        StatefulBuilder(
          builder: (context, setState) {
            return Checkbox(
              value: false, // This will be overridden in TaskCardNew
              onChanged: (value) {}, // This will be overridden in TaskCardNew
            );
          },
        ),
      ];
      _subtaskActions = [
        StatefulBuilder(
          builder: (context, setState) {
            return Checkbox(
              value: false, // This will be overridden in TaskCardNew
              onChanged: (value) {}, // This will be overridden in TaskCardNew
            );
          },
        ),
      ];
    } else {
      _parentActions = [
        TaskActionButton(
          icon: AppAssets.taskStore,
          actionType: ActionTypes.contactInfo,
          onPressed: () {
            if (widget.onParentActionTap != null) {
              // We'll pass the first task of the current group as the parent task
              final parentTask = _tasksNew.isNotEmpty ? _tasksNew[0][0] : null;
              if (parentTask != null) {
                widget.onParentActionTap!(ActionTypes.contactInfo, parentTask);
              }
            } else {
              SnackBarService.info(
                context: context,
                message: 'Contact info',
              );
            }
          },
        ),
        TaskActionButton(
          icon: AppAssets.appbarMap,
          actionType: ActionTypes.map,
          onPressed: () {
            if (widget.onParentActionTap != null) {
              final parentTask = _tasksNew.isNotEmpty ? _tasksNew[0][0] : null;
              if (parentTask != null) {
                widget.onParentActionTap!(ActionTypes.map, parentTask);
              }
            } else {
              SnackBarService.info(
                context: context,
                message: 'Map',
              );
            }
          },
        ),
        TaskActionButton(
          icon: AppAssets.taskAssistant,
          actionType: ActionTypes.chatAssistant,
          onPressed: () {
            if (widget.onParentActionTap != null) {
              final parentTask = _tasksNew.isNotEmpty ? _tasksNew[0][0] : null;
              if (parentTask != null) {
                widget.onParentActionTap!(
                    ActionTypes.chatAssistant, parentTask);
              }
            } else {
              SnackBarService.info(
                context: context,
                message: 'Chat with assistant',
              );
            }
          },
        ),
        // TaskActionButton(
        //   icon: Icons.assignment_outlined,
        //   actionType: ActionTypes.taskDetails,
        //   onPressed: () {
        //     if (widget.onParentActionTap != null) {
        //       final parentTask = _tasksNew.isNotEmpty ? _tasksNew[0][0] : null;
        //       if (parentTask != null) {
        //         widget.onParentActionTap!(ActionTypes.taskDetails, parentTask);
        //       }
        //     } else {
        //       SnackBarService.info(
        //         context: context,
        //         message: 'Task details',
        //       );
        //     }
        //   },
        // ),
      ];

      _subtaskActions = [
        TaskActionButton(
          icon: AppAssets.taskAssistant,
          actionType: ActionTypes.changeHelper,
          onPressed: () {
            if (widget.onSubtaskActionTap != null) {
              // The actual subtask will be provided when the action is used in the TaskCardNew
              // This is just a template that will be used for each subtask
              widget.onSubtaskActionTap!(
                  ActionTypes.changeHelper, TaskDetail());
            } else {
              SnackBarService.info(
                context: context,
                message: 'Change helper',
              );
            }
          },
        ),
        TaskActionButton(
          icon: AppAssets.taskReport,
          actionType: ActionTypes.viewDocument,
          onPressed: () {
            if (widget.onSubtaskActionTap != null) {
              widget.onSubtaskActionTap!(
                  ActionTypes.viewDocument, TaskDetail());
            } else {
              SnackBarService.info(
                context: context,
                message: 'View document',
              );
            }
          },
        ),
        TaskActionButton(
          icon: AppAssets.posIcon,
          actionType: ActionTypes.viewPos,
          onPressed: () {
            if (widget.onSubtaskActionTap != null) {
              widget.onSubtaskActionTap!(ActionTypes.viewPos, TaskDetail());
            } else {
              SnackBarService.info(
                context: context,
                message: 'View POS',
              );
            }
          },
        ),
        TaskActionButton(
          icon: AppAssets.taskReport,
          actionType: ActionTypes.viewNote,
          onPressed: () {
            if (widget.onSubtaskActionTap != null) {
              widget.onSubtaskActionTap!(ActionTypes.viewNote, TaskDetail());
            } else {
              SnackBarService.info(
                context: context,
                message: 'View note',
              );
            }
          },
        ),
        TaskActionButton(
          icon: AppAssets.posIcon,
          actionType: ActionTypes.viewBrief,
          onPressed: () {
            if (widget.onSubtaskActionTap != null) {
              widget.onSubtaskActionTap!(ActionTypes.viewBrief, TaskDetail());
            } else {
              SnackBarService.info(
                context: context,
                message: 'View brief',
              );
            }
          },
        ),
        TaskActionButton(
          icon: AppAssets.taskAssistant,
          actionType: ActionTypes.chatAssistant,
          onPressed: () {
            if (widget.onSubtaskActionTap != null) {
              widget.onSubtaskActionTap!(
                  ActionTypes.chatAssistant, TaskDetail());
            } else {
              SnackBarService.info(
                context: context,
                message: 'Chat with assistant',
              );
            }
          },
        ),
      ];
    }
  }

  @override
  void didUpdateWidget(covariant ReorderableStoreList oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.tasks != widget.tasks ||
        oldWidget.isCalendarMode != widget.isCalendarMode) {
      _tasks = List<TaskDetail>.from(widget.tasks);
      // Initialize with default grouping first
      _tasksNew = groupTasksByStore(_tasks);
      _buildActionButtons();

      // Preserve checkbox states when tasks are updated
      // Only initialize new tasks, don't reset existing ones
      for (var taskGroup in _tasksNew) {
        for (var task in taskGroup) {
          final taskId = task.taskId.toString();
          if (!_checkboxStates.containsKey(taskId)) {
            _checkboxStates[taskId] = false;
          }
        }
      }

      // Load saved order asynchronously after updating tasks
      _loadSavedTaskOrder();
    }

    // Apply select all state if it changed - handle it directly
    if (oldWidget.selectAll != widget.selectAll) {
      _doSelectAll(widget.selectAll);
    }
  }

  void _handleSubtaskReorder(int taskIndex, int oldIndex, int newIndex) {
    setState(() {
      if (taskIndex < 0 || taskIndex >= _tasksNew.length) return;

      final taskGroup = _tasksNew[taskIndex];
      if (oldIndex < newIndex) {
        newIndex -= 1;
      }
      final task = taskGroup.removeAt(oldIndex);
      taskGroup.insert(newIndex, task);

      // Update the main tasks list to reflect the changes
      _tasks = _tasksNew.expand((group) => group).toList();
    });

    // Save the new task order after reordering
    _saveCurrentTaskOrder();
  }

  void _handleTaskReorder(int oldIndex, int newIndex) {
    setState(() {
      if (oldIndex < newIndex) newIndex--;
      final taskGroup = _tasksNew.removeAt(oldIndex);
      _tasksNew.insert(newIndex, taskGroup);

      // Update the main tasks list to reflect the changes
      _tasks = _tasksNew.expand((group) => group).toList();
    });

    // Save the new task order after reordering
    _saveCurrentTaskOrder();
  }

  @override
  Widget build(BuildContext context) {
    // If there are no tasks, return an empty container
    if (_tasksNew.isEmpty) {
      return const Center(
        child: Text('No unscheduled tasks available'),
      );
    }

    int taskIndex = 0;
    List<Widget> listItems = [];
    for (var task1 in _tasksNew) {
      // Skip empty task groups
      if (task1.isEmpty) continue;

      var task = task1.first;
      final String keyValue =
          'task_${task.taskId}_${task.location ?? "unknown"}';

      listItems.add(
        ReorderableDelayedDragStartListener(
          index: taskIndex,
          key: ValueKey('drag_$keyValue'),
          enabled: true,
          child: StoreCard(
            heading: 'Priority tasks',
            key: ValueKey(keyValue),
            task: task1,
            parentActions: _parentActions,
            subTaskActions: _subtaskActions,
            isCalendarMode: widget.isCalendarMode,
            showScheduledDate: widget.showScheduledDate,
            showTickIndicator: widget.showTickIndicator,
            showAllDisclosureIndicator: widget.showAllDisclosureIndicator,
            permanentlyDisableAllDisclosureIndicator:
                widget.permanentlyDisableAllDisclosureIndicator,
            isOpenTask: widget.isOpenTask,
            // Pass checkbox states
            initialParentCheckboxValue:
                _checkboxStates[task.taskId.toString()] ?? false,
            initialChildCheckboxStates: Map.fromEntries(task1.map((subtask) =>
                MapEntry(subtask.taskId.toString(),
                    _checkboxStates[subtask.taskId.toString()] ?? false))),
            onSubtaskReorder: (oldIndex, newIndex) {
              _handleSubtaskReorder(taskIndex, oldIndex, newIndex);
            },
            onParentSelectionChanged: (isSelected, parentTask) {
              _handleParentCheckboxChanged(isSelected, parentTask);
            },
            onSubtaskSelectionChanged: (isSelected, subtask) {
              _handleSubtaskCheckboxChanged(isSelected, subtask);
            },
            onSubtaskActionTap: widget.onSubtaskActionTap,
          ),
        ),
      );
      taskIndex++;
    }

    // If we have no valid items, show a message
    if (listItems.isEmpty) {
      return const Center(
        child: Text('No valid tasks to display'),
      );
    }

    // Wrap in LayoutBuilder to ensure proper constraints
    return LayoutBuilder(builder: (context, constraints) {
      return ReorderableListView(
        physics: const ClampingScrollPhysics(), // Keep original scroll physics
        buildDefaultDragHandles: false,
        shrinkWrap: true, // Ensure it takes only the space it needs
        padding:
            const EdgeInsets.symmetric(vertical: 8.0), // Add vertical padding
        onReorderStart: (index) {
          HapticFeedback.mediumImpact();
        },
        onReorder: _handleTaskReorder,
        proxyDecorator: (child, index, animation) {
          // Add a spring animation to the dragged item
          final curvedAnimation = CurvedAnimation(
            parent: animation,
            curve: Curves.easeInOut,
            reverseCurve: Curves.easeInOut,
          );

          return AnimatedBuilder(
            animation: curvedAnimation,
            builder: (context, child) {
              final scale =
                  1.0 + (curvedAnimation.value * 0.02); // Subtle scale effect

              return Material(
                elevation: 4 * curvedAnimation.value,
                color: Colors.transparent,
                shadowColor: Colors.black.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(12),
                child: Transform.scale(
                  scale: scale,
                  child: child,
                ),
              );
            },
            child: child,
          );
        },
        children: listItems,
      );
    });
  }
}
