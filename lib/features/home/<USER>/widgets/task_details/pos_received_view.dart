import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/constants/app_assets.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;

class PosReceivedView extends StatelessWidget {
  final entities.TaskDetail task;

  const PosReceivedView({super.key, required this.task});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('POS Received', style: textTheme.montserratTitleXxsmall),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                AppAssets.dashboardPos,
                width: 28,
              ),
              const Gap(8),
              Text(
                task.posReceived != null ? task.posReceived.toString() : '0',
                style: textTheme.titleSmall?.copyWith(
                  fontSize: 40,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Center(
                child: SizedBox(
                  height: 30,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        ' of ${task.posItems != null ? task.posItems!.length : '0'}',
                        style: textTheme.bodySmall?.copyWith(
                          color: AppColors.black,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          Text(
            'Delivered',
            style: textTheme.montserratTableSmall.copyWith(
                color: AppColors.primaryBlue, fontStyle: FontStyle.italic),
          ),
        ],
      ),
    );
  }
}
