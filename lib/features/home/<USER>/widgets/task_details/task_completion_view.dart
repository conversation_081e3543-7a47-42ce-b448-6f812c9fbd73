import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/widgets/segment_indicator.dart';

class TaskCompletionView extends StatelessWidget {
  final entities.TaskDetail task;
  final int max;
  final int completed;

  const TaskCompletionView({
    super.key,
    required this.task,
    required this.max,
    required this.completed,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 22),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text('Task Completion', style: textTheme.montserratTitleXxsmall),
          const Gap(8),
          Row(
            children: [
              Text(
                (completed == 0 && max == 0)
                    ? '0%'
                    : '${((completed / max) * 100).toStringAsFixed(0)}%',
                style: textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const Gap(8),
          LayoutBuilder(
            builder: (context, constraints) {
              final progressWidth = constraints.maxWidth;
              final adjustedPosition =
                  (completed == 0 && max == 0) ? 0.0 : (completed / max) - 0.75;

              return Stack(
                clipBehavior: Clip.none,
                children: [
                  SegmentedProgressIndicator(
                    progress:
                        (completed == 0 && max == 0) ? 0.0 : (completed / max),
                    totalWidth: progressWidth,
                    activeColor: AppColors.primaryBlue,
                    backgroundColor: Colors.grey.shade200,
                    dividerColor: Colors.black,
                    height: 10,
                    segments: 10,
                    borderRadius: 10,
                  ),
                  Positioned(
                    left: adjustedPosition,
                    top: -8,
                    bottom: 0,
                    child: Container(
                      width: 1.5,
                      color: Colors.black,
                    ),
                  ),
                ],
              );
            },
          ),
          const Gap(8),
          Text(
              '${task.ctFormsCompletedCnt ?? 0} of ${task.forms?.length ?? 0} forms',
              style: textTheme.montserratTableSmall),
        ],
      ),
    );
  }
}
