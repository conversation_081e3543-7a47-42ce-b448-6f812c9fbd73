import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/constants/app_assets.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';

import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';

class TaskDetailsAppBar extends StatelessWidget implements PreferredSizeWidget {
  final entities.TaskDetail? task;
  final bool showAlert;
  final bool showDocuments;
  final VoidCallback onAlertTap;
  final VoidCallback onDocumentsTap;
  final VoidCallback? onDirectionsTap;

  const TaskDetailsAppBar({
    super.key,
    required this.task,
    required this.showAlert,
    required this.showDocuments,
    required this.onAlertTap,
    required this.onDocumentsTap,
    this.onDirectionsTap,
  });

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context) {
    return CustomAppBar(
      title: 'Task details',
      actions: [
        GestureDetector(
          onTap: onAlertTap,
          child: Image.asset(
            AppAssets.alertIcon,
            scale: 4,
            color: showAlert ? AppColors.primaryBlue : AppColors.black,
          ),
        ),
        const Gap(18),
        GestureDetector(
          onTap: onDocumentsTap,
          child: Image.asset(
            AppAssets.documentsIcon,
            color: showDocuments ? AppColors.primaryBlue : AppColors.black,
            scale: 4,
          ),
        ),
        const Gap(8),
        _buildPopupMenu(context, task),
      ],
    );
  }

  PopupMenuButton<String> _buildPopupMenu(
      BuildContext context, entities.TaskDetail? task) {
    return PopupMenuButton<String>(
      icon: Image.asset(
        AppAssets.taskMore,
        scale: 4,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      elevation: 8,
      offset: const Offset(0, 20),
      color: Colors.white,
      position: PopupMenuPosition.under,
      constraints: const BoxConstraints(
        minWidth: 240,
        maxWidth: 320,
      ),
      itemBuilder: (context) => [
        _buildPopupMenuItem(context, 'Forms', AppAssets.taskForm),
        const PopupMenuDivider(height: .2),
        _buildPopupMenuItem(context, 'POS', AppAssets.posIcon),
        const PopupMenuDivider(height: .2),
        _buildPopupMenuItem(context, 'Note', AppAssets.alertMessage),
        const PopupMenuDivider(height: .2),
        _buildPopupMenuItem(context, 'Directions', AppAssets.appbarMap),
        const PopupMenuDivider(height: .2),
        _buildPopupMenuItem(context, 'Store info', AppAssets.taskStore),
        const PopupMenuDivider(height: .2),
        _buildPopupMenuItem(
            context, 'Store history', AppAssets.taskStoryHistory),
        const PopupMenuDivider(height: .2),
        _buildPopupMenuItem(
            context, 'Task assistance', AppAssets.taskAssistant),
        const PopupMenuDivider(height: .2),
        _buildPopupMenuItem(context, 'Complete task', AppAssets.taskComplete,
            isBlue: true),
      ],
      onSelected: (value) =>
          task != null ? _handleMenuSelection(context, value, task) : null,
    );
  }

  PopupMenuItem<String> _buildPopupMenuItem(
      BuildContext context, String title, String icon,
      {bool isBlue = false}) {
    return PopupMenuItem<String>(
      value: title,
      height: 48,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.montserratTitleSmall.copyWith(
                  color: isBlue ? AppColors.primaryBlue : Colors.black,
                ),
          ),
          const SizedBox(width: 16),
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Center(
              child: Image.asset(
                icon,
                scale: 3,
                color: Colors.black,
                fit: BoxFit.cover,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleMenuSelection(
      BuildContext context, String value, entities.TaskDetail task) {
    switch (value) {
      case 'Forms':
        context.navigateTo(FormRoute(
          task: task,
        ));
        break;
      case 'POS':
        context.navigateTo(PosRoute(
          task: task,
        ));
        break;
      case 'Note':
        context.navigateTo(NotesRoute(
          task: task,
        ));
        break;
      case 'Directions':
        if (onDirectionsTap != null) {
          onDirectionsTap!();
        }
        break;
      case 'Store info':
        context.navigateTo(const StoreInfoRoute());
        break;
      case 'Store history':
        context.navigateTo(StoreHistoryRoute(
          storeId: (task.storeId ?? 0).toInt(),
          taskId: (task.taskId ?? 0).toInt(),
        ));
        break;
      case 'Task assistance':
      case 'Complete task':
        break;
    }
  }
}
