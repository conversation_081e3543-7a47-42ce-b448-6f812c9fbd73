import 'package:flutter/material.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as schedule;
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/reorderable_store_list.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/date_task_header.dart';

class WeekTasksList extends StatelessWidget {
  final List<schedule.TaskDetail> allApiTasks;
  final List<DateTime> weekDays;
  final bool isCheckboxMode;
  final bool areAllItemsSelected;
  final Function(String, TaskDetail) onParentActionTap;
  final Function(String, TaskDetail) onSubtaskActionTap;
  final Function(List<TaskDetail>) onSelectionChanged;
  final TaskDetail Function(schedule.TaskDetail) convertScheduleToDatum;
  final String Function(List<schedule.TaskDetail>) calculateTotalHours;

  const WeekTasksList({
    super.key,
    required this.allApiTasks,
    required this.weekDays,
    required this.isCheckboxMode,
    required this.areAllItemsSelected,
    required this.onParentActionTap,
    required this.onSubtaskActionTap,
    required this.onSelectionChanged,
    required this.convertScheduleToDatum,
    required this.calculateTotalHours,
  });

  @override
  Widget build(BuildContext context) {
    // Get tasks for the entire week
    Map<DateTime, List<schedule.TaskDetail>> tasksByDate = _getTasksForWeek();

    // Check if there are any tasks for the entire week
    bool hasAnyTasks = false;
    tasksByDate.forEach((date, tasks) {
      if (tasks.isNotEmpty) {
        hasAnyTasks = true;
      }
    });

    // If no tasks for the entire week, show a message
    if (!hasAnyTasks) {
      return const EmptyState(message: 'No tasks for this week');
    }

    // Create a list of widgets for each date with its tasks
    List<Widget> dateTaskWidgets = [];

    // Sort the dates
    List<DateTime> sortedDates = tasksByDate.keys.toList()
      ..sort((a, b) => a.compareTo(b));

    // Build widgets for each date
    for (DateTime date in sortedDates) {
      List<schedule.TaskDetail> tasksForDate = tasksByDate[date]!;

      // Add date header
      dateTaskWidgets.add(
        DateTaskHeader(
          date: date,
          tasksForDate: tasksForDate,
          calculateTotalHours: calculateTotalHours,
        ),
      );

      // Add divider
      tasksForDate.isEmpty
          ? dateTaskWidgets.add(const Divider(height: 1))
          : const SizedBox();

      // If no tasks for this date, show empty state message
      if (tasksForDate.isNotEmpty) {
        // Convert to Datum for compatibility
        List<TaskDetail> convertedTasks =
            tasksForDate.map((task) => convertScheduleToDatum(task)).toList();

        // Add tasks for this date
        dateTaskWidgets.add(
          Container(
            constraints: const BoxConstraints(minHeight: 0),
            child: ReorderableStoreList(
              tasks: convertedTasks,
              isCalendarMode: isCheckboxMode,
              showScheduledDate: false,
              showTickIndicator: true,
              showAllDisclosureIndicator: false,
              permanentlyDisableAllDisclosureIndicator: false,
              isOpenTask: true,
              onParentActionTap: onParentActionTap,
              onSubtaskActionTap: onSubtaskActionTap,
              onSelectionChanged: onSelectionChanged,
              selectAll: areAllItemsSelected,
            ),
          ),
        );
      }

      tasksForDate.isNotEmpty
          ? dateTaskWidgets.add(const Divider(height: 1))
          : const SizedBox();
    }

    // Return a Column with all date sections
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: dateTaskWidgets,
    );
  }

  // Get tasks for the entire week grouped by date, including days with no tasks
  Map<DateTime, List<schedule.TaskDetail>> _getTasksForWeek() {
    Map<DateTime, List<schedule.TaskDetail>> tasksByDate = {};

    // Initialize the map with all days in the week (empty lists)
    for (var weekDay in weekDays) {
      final normalizedDate = DateTime(weekDay.year, weekDay.month, weekDay.day);
      tasksByDate[normalizedDate] = [];
    }

    // Process all API tasks
    for (var task in allApiTasks) {
      // Check if the task is confirmed and not open
      if (task.taskStatus != "Confirmed" ||
          task.isOpen == true ||
          task.scheduledTimeStamp == null) {
        continue;
      }

      // Check if the task's date is in the current week
      for (var weekDay in weekDays) {
        final weekDayDate = DateTime(weekDay.year, weekDay.month, weekDay.day);
        final taskDate = DateTime(task.scheduledTimeStamp!.year,
            task.scheduledTimeStamp!.month, task.scheduledTimeStamp!.day);

        if (weekDayDate.isAtSameMomentAs(taskDate)) {
          // Create a normalized date (without time) for grouping
          final normalizedDate = DateTime(task.scheduledTimeStamp!.year,
              task.scheduledTimeStamp!.month, task.scheduledTimeStamp!.day);

          // Add to list
          tasksByDate[normalizedDate]!.add(task);
          break;
        }
      }
    }

    // Sort tasks within each date by store name and scheduled time
    tasksByDate.forEach((date, tasks) {
      tasks.sort((a, b) => (a.storeName ?? '').compareTo(b.storeName ?? ''));
      tasks.sort((a, b) => (a.scheduledTimeStamp ?? DateTime.now())
          .compareTo(b.scheduledTimeStamp ?? DateTime.now()));
    });

    return tasksByDate;
  }
}
