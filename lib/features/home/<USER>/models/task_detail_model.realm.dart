// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'task_detail_model.dart';

// **************************************************************************
// RealmObjectGenerator
// **************************************************************************

// ignore_for_file: type=lint
class TaskDetailModel extends _TaskDetailModel
    with RealmEntity, RealmObjectBase, RealmObject {
  static var _defaultsSet = false;

  TaskDetailModel(
    int id, {
    bool isSynced = false,
    int? taskId,
    int? projectId,
    int? scheduleId,
    bool? sentToPayroll,
    bool? showKm,
    int? storeId,
    String? client,
    int? clientId,
    String? clientLogoUrl,
    String? storeGroup,
    int? storeGroupId,
    String? storeName,
    String? storeEmail,
    int? minutes,
    int? budget,
    int? originalbudget,
    String? comment,
    int? claimableKms,
    int? flightDuration,
    int? pages,
    String? location,
    String? suburb,
    double? latitude,
    double? longitude,
    double? taskLatitude,
    double? taskLongitude,
    String? cycle,
    int? cycleId,
    bool? canDelete,
    DateTime? scheduledTimeStamp,
    DateTime? submissionTimeStamp,
    DateTime? expires,
    String? onTask,
    String? phone,
    DateTime? rangeStart,
    DateTime? rangeEnd,
    bool? reOpened,
    String? reOpenedReason,
    String? taskStatus,
    int? warehousejobId,
    String? connoteUrl,
    bool? posRequired,
    bool? isPosMandatory,
    String? posReceived,
    Iterable<PhotoFolderModel> photoFolder = const [],
    Iterable<SignatureFolderModel> signatureFolder = const [],
    Iterable<FormModel> forms = const [],
    Iterable<PosItemModel> posItems = const [],
    Iterable<DocumentModel> documents = const [],
    Iterable<TaskalertModel> taskalerts = const [],
    Iterable<TaskmemberModel> taskmembers = const [],
    Iterable<PhotoTagsTModel> photoTagsTwo = const [],
    Iterable<PhotoTagsTModel> photoTagsThree = const [],
    DateTime? modifiedTimeStampDocuments,
    DateTime? modifiedTimeStampForms,
    DateTime? modifiedTimeStampMembers,
    DateTime? modifiedTimeStampTask,
    DateTime? modifiedTimeStampPhotos,
    DateTime? modifiedTimeStampSignatures,
    DateTime? modifiedTimeStampSignaturetypes,
    String? posSentTo,
    String? posSentToEmail,
    DateTime? modifiedTimeStampPhototypes,
    DateTime? taskCommencementTimeStamp,
    DateTime? taskStoppedTimeStamp,
    int? teamlead,
    Iterable<FollowupTaskModel> followupTasks = const [],
    Iterable<StocktakeModel> stocktake = const [],
    String? taskNote,
    bool? disallowReschedule,
    int? photoResPerc,
    bool? liveImagesOnly,
    String? timeSchedule,
    int? scheduleTypeId,
    bool? showFollowupIconMulti,
    bool? followupSelectedMulti,
    int? regionId,
    bool? isOpen,
    int? taskCount,
    int? ctFormsTotalCnt,
    int? ctFormsCompletedCnt,
    String? preftime,
    bool? sendTo,
    int? kTotal,
    int? kCompleted,
  }) {
    if (!_defaultsSet) {
      _defaultsSet = RealmObjectBase.setDefaults<TaskDetailModel>({
        'id': 0,
        'isSynced': false,
      });
    }
    RealmObjectBase.set(this, 'id', id);
    RealmObjectBase.set(this, 'isSynced', isSynced);
    RealmObjectBase.set(this, 'taskId', taskId);
    RealmObjectBase.set(this, 'projectId', projectId);
    RealmObjectBase.set(this, 'scheduleId', scheduleId);
    RealmObjectBase.set(this, 'sentToPayroll', sentToPayroll);
    RealmObjectBase.set(this, 'showKm', showKm);
    RealmObjectBase.set(this, 'storeId', storeId);
    RealmObjectBase.set(this, 'client', client);
    RealmObjectBase.set(this, 'clientId', clientId);
    RealmObjectBase.set(this, 'clientLogoUrl', clientLogoUrl);
    RealmObjectBase.set(this, 'storeGroup', storeGroup);
    RealmObjectBase.set(this, 'storeGroupId', storeGroupId);
    RealmObjectBase.set(this, 'storeName', storeName);
    RealmObjectBase.set(this, 'storeEmail', storeEmail);
    RealmObjectBase.set(this, 'minutes', minutes);
    RealmObjectBase.set(this, 'budget', budget);
    RealmObjectBase.set(this, 'originalbudget', originalbudget);
    RealmObjectBase.set(this, 'comment', comment);
    RealmObjectBase.set(this, 'claimableKms', claimableKms);
    RealmObjectBase.set(this, 'flightDuration', flightDuration);
    RealmObjectBase.set(this, 'pages', pages);
    RealmObjectBase.set(this, 'location', location);
    RealmObjectBase.set(this, 'suburb', suburb);
    RealmObjectBase.set(this, 'latitude', latitude);
    RealmObjectBase.set(this, 'longitude', longitude);
    RealmObjectBase.set(this, 'taskLatitude', taskLatitude);
    RealmObjectBase.set(this, 'taskLongitude', taskLongitude);
    RealmObjectBase.set(this, 'cycle', cycle);
    RealmObjectBase.set(this, 'cycleId', cycleId);
    RealmObjectBase.set(this, 'canDelete', canDelete);
    RealmObjectBase.set(this, 'scheduledTimeStamp', scheduledTimeStamp);
    RealmObjectBase.set(this, 'submissionTimeStamp', submissionTimeStamp);
    RealmObjectBase.set(this, 'expires', expires);
    RealmObjectBase.set(this, 'onTask', onTask);
    RealmObjectBase.set(this, 'phone', phone);
    RealmObjectBase.set(this, 'rangeStart', rangeStart);
    RealmObjectBase.set(this, 'rangeEnd', rangeEnd);
    RealmObjectBase.set(this, 'reOpened', reOpened);
    RealmObjectBase.set(this, 'reOpenedReason', reOpenedReason);
    RealmObjectBase.set(this, 'taskStatus', taskStatus);
    RealmObjectBase.set(this, 'warehousejobId', warehousejobId);
    RealmObjectBase.set(this, 'connoteUrl', connoteUrl);
    RealmObjectBase.set(this, 'posRequired', posRequired);
    RealmObjectBase.set(this, 'isPosMandatory', isPosMandatory);
    RealmObjectBase.set(this, 'posReceived', posReceived);
    RealmObjectBase.set<RealmList<PhotoFolderModel>>(
        this, 'photoFolder', RealmList<PhotoFolderModel>(photoFolder));
    RealmObjectBase.set<RealmList<SignatureFolderModel>>(this,
        'signatureFolder', RealmList<SignatureFolderModel>(signatureFolder));
    RealmObjectBase.set<RealmList<FormModel>>(
        this, 'forms', RealmList<FormModel>(forms));
    RealmObjectBase.set<RealmList<PosItemModel>>(
        this, 'posItems', RealmList<PosItemModel>(posItems));
    RealmObjectBase.set<RealmList<DocumentModel>>(
        this, 'documents', RealmList<DocumentModel>(documents));
    RealmObjectBase.set<RealmList<TaskalertModel>>(
        this, 'taskalerts', RealmList<TaskalertModel>(taskalerts));
    RealmObjectBase.set<RealmList<TaskmemberModel>>(
        this, 'taskmembers', RealmList<TaskmemberModel>(taskmembers));
    RealmObjectBase.set<RealmList<PhotoTagsTModel>>(
        this, 'photoTagsTwo', RealmList<PhotoTagsTModel>(photoTagsTwo));
    RealmObjectBase.set<RealmList<PhotoTagsTModel>>(
        this, 'photoTagsThree', RealmList<PhotoTagsTModel>(photoTagsThree));
    RealmObjectBase.set(
        this, 'modifiedTimeStampDocuments', modifiedTimeStampDocuments);
    RealmObjectBase.set(this, 'modifiedTimeStampForms', modifiedTimeStampForms);
    RealmObjectBase.set(
        this, 'modifiedTimeStampMembers', modifiedTimeStampMembers);
    RealmObjectBase.set(this, 'modifiedTimeStampTask', modifiedTimeStampTask);
    RealmObjectBase.set(
        this, 'modifiedTimeStampPhotos', modifiedTimeStampPhotos);
    RealmObjectBase.set(
        this, 'modifiedTimeStampSignatures', modifiedTimeStampSignatures);
    RealmObjectBase.set(this, 'modifiedTimeStampSignaturetypes',
        modifiedTimeStampSignaturetypes);
    RealmObjectBase.set(this, 'posSentTo', posSentTo);
    RealmObjectBase.set(this, 'posSentToEmail', posSentToEmail);
    RealmObjectBase.set(
        this, 'modifiedTimeStampPhototypes', modifiedTimeStampPhototypes);
    RealmObjectBase.set(
        this, 'taskCommencementTimeStamp', taskCommencementTimeStamp);
    RealmObjectBase.set(this, 'taskStoppedTimeStamp', taskStoppedTimeStamp);
    RealmObjectBase.set(this, 'teamlead', teamlead);
    RealmObjectBase.set<RealmList<FollowupTaskModel>>(
        this, 'followupTasks', RealmList<FollowupTaskModel>(followupTasks));
    RealmObjectBase.set<RealmList<StocktakeModel>>(
        this, 'stocktake', RealmList<StocktakeModel>(stocktake));
    RealmObjectBase.set(this, 'taskNote', taskNote);
    RealmObjectBase.set(this, 'disallowReschedule', disallowReschedule);
    RealmObjectBase.set(this, 'photoResPerc', photoResPerc);
    RealmObjectBase.set(this, 'liveImagesOnly', liveImagesOnly);
    RealmObjectBase.set(this, 'timeSchedule', timeSchedule);
    RealmObjectBase.set(this, 'scheduleTypeId', scheduleTypeId);
    RealmObjectBase.set(this, 'showFollowupIconMulti', showFollowupIconMulti);
    RealmObjectBase.set(this, 'followupSelectedMulti', followupSelectedMulti);
    RealmObjectBase.set(this, 'regionId', regionId);
    RealmObjectBase.set(this, 'isOpen', isOpen);
    RealmObjectBase.set(this, 'taskCount', taskCount);
    RealmObjectBase.set(this, 'ctFormsTotalCnt', ctFormsTotalCnt);
    RealmObjectBase.set(this, 'ctFormsCompletedCnt', ctFormsCompletedCnt);
    RealmObjectBase.set(this, 'preftime', preftime);
    RealmObjectBase.set(this, 'sendTo', sendTo);
    RealmObjectBase.set(this, 'kTotal', kTotal);
    RealmObjectBase.set(this, 'kCompleted', kCompleted);
  }

  TaskDetailModel._();

  @override
  int get id => RealmObjectBase.get<int>(this, 'id') as int;
  @override
  set id(int value) => RealmObjectBase.set(this, 'id', value);

  @override
  bool get isSynced => RealmObjectBase.get<bool>(this, 'isSynced') as bool;
  @override
  set isSynced(bool value) => RealmObjectBase.set(this, 'isSynced', value);

  @override
  int? get taskId => RealmObjectBase.get<int>(this, 'taskId') as int?;
  @override
  set taskId(int? value) => RealmObjectBase.set(this, 'taskId', value);

  @override
  int? get projectId => RealmObjectBase.get<int>(this, 'projectId') as int?;
  @override
  set projectId(int? value) => RealmObjectBase.set(this, 'projectId', value);

  @override
  int? get scheduleId => RealmObjectBase.get<int>(this, 'scheduleId') as int?;
  @override
  set scheduleId(int? value) => RealmObjectBase.set(this, 'scheduleId', value);

  @override
  bool? get sentToPayroll =>
      RealmObjectBase.get<bool>(this, 'sentToPayroll') as bool?;
  @override
  set sentToPayroll(bool? value) =>
      RealmObjectBase.set(this, 'sentToPayroll', value);

  @override
  bool? get showKm => RealmObjectBase.get<bool>(this, 'showKm') as bool?;
  @override
  set showKm(bool? value) => RealmObjectBase.set(this, 'showKm', value);

  @override
  int? get storeId => RealmObjectBase.get<int>(this, 'storeId') as int?;
  @override
  set storeId(int? value) => RealmObjectBase.set(this, 'storeId', value);

  @override
  String? get client => RealmObjectBase.get<String>(this, 'client') as String?;
  @override
  set client(String? value) => RealmObjectBase.set(this, 'client', value);

  @override
  int? get clientId => RealmObjectBase.get<int>(this, 'clientId') as int?;
  @override
  set clientId(int? value) => RealmObjectBase.set(this, 'clientId', value);

  @override
  String? get clientLogoUrl =>
      RealmObjectBase.get<String>(this, 'clientLogoUrl') as String?;
  @override
  set clientLogoUrl(String? value) =>
      RealmObjectBase.set(this, 'clientLogoUrl', value);

  @override
  String? get storeGroup =>
      RealmObjectBase.get<String>(this, 'storeGroup') as String?;
  @override
  set storeGroup(String? value) =>
      RealmObjectBase.set(this, 'storeGroup', value);

  @override
  int? get storeGroupId =>
      RealmObjectBase.get<int>(this, 'storeGroupId') as int?;
  @override
  set storeGroupId(int? value) =>
      RealmObjectBase.set(this, 'storeGroupId', value);

  @override
  String? get storeName =>
      RealmObjectBase.get<String>(this, 'storeName') as String?;
  @override
  set storeName(String? value) => RealmObjectBase.set(this, 'storeName', value);

  @override
  String? get storeEmail =>
      RealmObjectBase.get<String>(this, 'storeEmail') as String?;
  @override
  set storeEmail(String? value) =>
      RealmObjectBase.set(this, 'storeEmail', value);

  @override
  int? get minutes => RealmObjectBase.get<int>(this, 'minutes') as int?;
  @override
  set minutes(int? value) => RealmObjectBase.set(this, 'minutes', value);

  @override
  int? get budget => RealmObjectBase.get<int>(this, 'budget') as int?;
  @override
  set budget(int? value) => RealmObjectBase.set(this, 'budget', value);

  @override
  int? get originalbudget =>
      RealmObjectBase.get<int>(this, 'originalbudget') as int?;
  @override
  set originalbudget(int? value) =>
      RealmObjectBase.set(this, 'originalbudget', value);

  @override
  String? get comment =>
      RealmObjectBase.get<String>(this, 'comment') as String?;
  @override
  set comment(String? value) => RealmObjectBase.set(this, 'comment', value);

  @override
  int? get claimableKms =>
      RealmObjectBase.get<int>(this, 'claimableKms') as int?;
  @override
  set claimableKms(int? value) =>
      RealmObjectBase.set(this, 'claimableKms', value);

  @override
  int? get flightDuration =>
      RealmObjectBase.get<int>(this, 'flightDuration') as int?;
  @override
  set flightDuration(int? value) =>
      RealmObjectBase.set(this, 'flightDuration', value);

  @override
  int? get pages => RealmObjectBase.get<int>(this, 'pages') as int?;
  @override
  set pages(int? value) => RealmObjectBase.set(this, 'pages', value);

  @override
  String? get location =>
      RealmObjectBase.get<String>(this, 'location') as String?;
  @override
  set location(String? value) => RealmObjectBase.set(this, 'location', value);

  @override
  String? get suburb => RealmObjectBase.get<String>(this, 'suburb') as String?;
  @override
  set suburb(String? value) => RealmObjectBase.set(this, 'suburb', value);

  @override
  double? get latitude =>
      RealmObjectBase.get<double>(this, 'latitude') as double?;
  @override
  set latitude(double? value) => RealmObjectBase.set(this, 'latitude', value);

  @override
  double? get longitude =>
      RealmObjectBase.get<double>(this, 'longitude') as double?;
  @override
  set longitude(double? value) => RealmObjectBase.set(this, 'longitude', value);

  @override
  double? get taskLatitude =>
      RealmObjectBase.get<double>(this, 'taskLatitude') as double?;
  @override
  set taskLatitude(double? value) =>
      RealmObjectBase.set(this, 'taskLatitude', value);

  @override
  double? get taskLongitude =>
      RealmObjectBase.get<double>(this, 'taskLongitude') as double?;
  @override
  set taskLongitude(double? value) =>
      RealmObjectBase.set(this, 'taskLongitude', value);

  @override
  String? get cycle => RealmObjectBase.get<String>(this, 'cycle') as String?;
  @override
  set cycle(String? value) => RealmObjectBase.set(this, 'cycle', value);

  @override
  int? get cycleId => RealmObjectBase.get<int>(this, 'cycleId') as int?;
  @override
  set cycleId(int? value) => RealmObjectBase.set(this, 'cycleId', value);

  @override
  bool? get canDelete => RealmObjectBase.get<bool>(this, 'canDelete') as bool?;
  @override
  set canDelete(bool? value) => RealmObjectBase.set(this, 'canDelete', value);

  @override
  DateTime? get scheduledTimeStamp =>
      RealmObjectBase.get<DateTime>(this, 'scheduledTimeStamp') as DateTime?;
  @override
  set scheduledTimeStamp(DateTime? value) =>
      RealmObjectBase.set(this, 'scheduledTimeStamp', value);

  @override
  DateTime? get submissionTimeStamp =>
      RealmObjectBase.get<DateTime>(this, 'submissionTimeStamp') as DateTime?;
  @override
  set submissionTimeStamp(DateTime? value) =>
      RealmObjectBase.set(this, 'submissionTimeStamp', value);

  @override
  DateTime? get expires =>
      RealmObjectBase.get<DateTime>(this, 'expires') as DateTime?;
  @override
  set expires(DateTime? value) => RealmObjectBase.set(this, 'expires', value);

  @override
  String? get onTask => RealmObjectBase.get<String>(this, 'onTask') as String?;
  @override
  set onTask(String? value) => RealmObjectBase.set(this, 'onTask', value);

  @override
  String? get phone => RealmObjectBase.get<String>(this, 'phone') as String?;
  @override
  set phone(String? value) => RealmObjectBase.set(this, 'phone', value);

  @override
  DateTime? get rangeStart =>
      RealmObjectBase.get<DateTime>(this, 'rangeStart') as DateTime?;
  @override
  set rangeStart(DateTime? value) =>
      RealmObjectBase.set(this, 'rangeStart', value);

  @override
  DateTime? get rangeEnd =>
      RealmObjectBase.get<DateTime>(this, 'rangeEnd') as DateTime?;
  @override
  set rangeEnd(DateTime? value) => RealmObjectBase.set(this, 'rangeEnd', value);

  @override
  bool? get reOpened => RealmObjectBase.get<bool>(this, 'reOpened') as bool?;
  @override
  set reOpened(bool? value) => RealmObjectBase.set(this, 'reOpened', value);

  @override
  String? get reOpenedReason =>
      RealmObjectBase.get<String>(this, 'reOpenedReason') as String?;
  @override
  set reOpenedReason(String? value) =>
      RealmObjectBase.set(this, 'reOpenedReason', value);

  @override
  String? get taskStatus =>
      RealmObjectBase.get<String>(this, 'taskStatus') as String?;
  @override
  set taskStatus(String? value) =>
      RealmObjectBase.set(this, 'taskStatus', value);

  @override
  int? get warehousejobId =>
      RealmObjectBase.get<int>(this, 'warehousejobId') as int?;
  @override
  set warehousejobId(int? value) =>
      RealmObjectBase.set(this, 'warehousejobId', value);

  @override
  String? get connoteUrl =>
      RealmObjectBase.get<String>(this, 'connoteUrl') as String?;
  @override
  set connoteUrl(String? value) =>
      RealmObjectBase.set(this, 'connoteUrl', value);

  @override
  bool? get posRequired =>
      RealmObjectBase.get<bool>(this, 'posRequired') as bool?;
  @override
  set posRequired(bool? value) =>
      RealmObjectBase.set(this, 'posRequired', value);

  @override
  bool? get isPosMandatory =>
      RealmObjectBase.get<bool>(this, 'isPosMandatory') as bool?;
  @override
  set isPosMandatory(bool? value) =>
      RealmObjectBase.set(this, 'isPosMandatory', value);

  @override
  String? get posReceived =>
      RealmObjectBase.get<String>(this, 'posReceived') as String?;
  @override
  set posReceived(String? value) =>
      RealmObjectBase.set(this, 'posReceived', value);

  @override
  RealmList<PhotoFolderModel> get photoFolder =>
      RealmObjectBase.get<PhotoFolderModel>(this, 'photoFolder')
          as RealmList<PhotoFolderModel>;
  @override
  set photoFolder(covariant RealmList<PhotoFolderModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  RealmList<SignatureFolderModel> get signatureFolder =>
      RealmObjectBase.get<SignatureFolderModel>(this, 'signatureFolder')
          as RealmList<SignatureFolderModel>;
  @override
  set signatureFolder(covariant RealmList<SignatureFolderModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  RealmList<FormModel> get forms =>
      RealmObjectBase.get<FormModel>(this, 'forms') as RealmList<FormModel>;
  @override
  set forms(covariant RealmList<FormModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  RealmList<PosItemModel> get posItems =>
      RealmObjectBase.get<PosItemModel>(this, 'posItems')
          as RealmList<PosItemModel>;
  @override
  set posItems(covariant RealmList<PosItemModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  RealmList<DocumentModel> get documents =>
      RealmObjectBase.get<DocumentModel>(this, 'documents')
          as RealmList<DocumentModel>;
  @override
  set documents(covariant RealmList<DocumentModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  RealmList<TaskalertModel> get taskalerts =>
      RealmObjectBase.get<TaskalertModel>(this, 'taskalerts')
          as RealmList<TaskalertModel>;
  @override
  set taskalerts(covariant RealmList<TaskalertModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  RealmList<TaskmemberModel> get taskmembers =>
      RealmObjectBase.get<TaskmemberModel>(this, 'taskmembers')
          as RealmList<TaskmemberModel>;
  @override
  set taskmembers(covariant RealmList<TaskmemberModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  RealmList<PhotoTagsTModel> get photoTagsTwo =>
      RealmObjectBase.get<PhotoTagsTModel>(this, 'photoTagsTwo')
          as RealmList<PhotoTagsTModel>;
  @override
  set photoTagsTwo(covariant RealmList<PhotoTagsTModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  RealmList<PhotoTagsTModel> get photoTagsThree =>
      RealmObjectBase.get<PhotoTagsTModel>(this, 'photoTagsThree')
          as RealmList<PhotoTagsTModel>;
  @override
  set photoTagsThree(covariant RealmList<PhotoTagsTModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  DateTime? get modifiedTimeStampDocuments =>
      RealmObjectBase.get<DateTime>(this, 'modifiedTimeStampDocuments')
          as DateTime?;
  @override
  set modifiedTimeStampDocuments(DateTime? value) =>
      RealmObjectBase.set(this, 'modifiedTimeStampDocuments', value);

  @override
  DateTime? get modifiedTimeStampForms =>
      RealmObjectBase.get<DateTime>(this, 'modifiedTimeStampForms')
          as DateTime?;
  @override
  set modifiedTimeStampForms(DateTime? value) =>
      RealmObjectBase.set(this, 'modifiedTimeStampForms', value);

  @override
  DateTime? get modifiedTimeStampMembers =>
      RealmObjectBase.get<DateTime>(this, 'modifiedTimeStampMembers')
          as DateTime?;
  @override
  set modifiedTimeStampMembers(DateTime? value) =>
      RealmObjectBase.set(this, 'modifiedTimeStampMembers', value);

  @override
  DateTime? get modifiedTimeStampTask =>
      RealmObjectBase.get<DateTime>(this, 'modifiedTimeStampTask') as DateTime?;
  @override
  set modifiedTimeStampTask(DateTime? value) =>
      RealmObjectBase.set(this, 'modifiedTimeStampTask', value);

  @override
  DateTime? get modifiedTimeStampPhotos =>
      RealmObjectBase.get<DateTime>(this, 'modifiedTimeStampPhotos')
          as DateTime?;
  @override
  set modifiedTimeStampPhotos(DateTime? value) =>
      RealmObjectBase.set(this, 'modifiedTimeStampPhotos', value);

  @override
  DateTime? get modifiedTimeStampSignatures =>
      RealmObjectBase.get<DateTime>(this, 'modifiedTimeStampSignatures')
          as DateTime?;
  @override
  set modifiedTimeStampSignatures(DateTime? value) =>
      RealmObjectBase.set(this, 'modifiedTimeStampSignatures', value);

  @override
  DateTime? get modifiedTimeStampSignaturetypes =>
      RealmObjectBase.get<DateTime>(this, 'modifiedTimeStampSignaturetypes')
          as DateTime?;
  @override
  set modifiedTimeStampSignaturetypes(DateTime? value) =>
      RealmObjectBase.set(this, 'modifiedTimeStampSignaturetypes', value);

  @override
  String? get posSentTo =>
      RealmObjectBase.get<String>(this, 'posSentTo') as String?;
  @override
  set posSentTo(String? value) => RealmObjectBase.set(this, 'posSentTo', value);

  @override
  String? get posSentToEmail =>
      RealmObjectBase.get<String>(this, 'posSentToEmail') as String?;
  @override
  set posSentToEmail(String? value) =>
      RealmObjectBase.set(this, 'posSentToEmail', value);

  @override
  DateTime? get modifiedTimeStampPhototypes =>
      RealmObjectBase.get<DateTime>(this, 'modifiedTimeStampPhototypes')
          as DateTime?;
  @override
  set modifiedTimeStampPhototypes(DateTime? value) =>
      RealmObjectBase.set(this, 'modifiedTimeStampPhototypes', value);

  @override
  DateTime? get taskCommencementTimeStamp =>
      RealmObjectBase.get<DateTime>(this, 'taskCommencementTimeStamp')
          as DateTime?;
  @override
  set taskCommencementTimeStamp(DateTime? value) =>
      RealmObjectBase.set(this, 'taskCommencementTimeStamp', value);

  @override
  DateTime? get taskStoppedTimeStamp =>
      RealmObjectBase.get<DateTime>(this, 'taskStoppedTimeStamp') as DateTime?;
  @override
  set taskStoppedTimeStamp(DateTime? value) =>
      RealmObjectBase.set(this, 'taskStoppedTimeStamp', value);

  @override
  int? get teamlead => RealmObjectBase.get<int>(this, 'teamlead') as int?;
  @override
  set teamlead(int? value) => RealmObjectBase.set(this, 'teamlead', value);

  @override
  RealmList<FollowupTaskModel> get followupTasks =>
      RealmObjectBase.get<FollowupTaskModel>(this, 'followupTasks')
          as RealmList<FollowupTaskModel>;
  @override
  set followupTasks(covariant RealmList<FollowupTaskModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  RealmList<StocktakeModel> get stocktake =>
      RealmObjectBase.get<StocktakeModel>(this, 'stocktake')
          as RealmList<StocktakeModel>;
  @override
  set stocktake(covariant RealmList<StocktakeModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  String? get taskNote =>
      RealmObjectBase.get<String>(this, 'taskNote') as String?;
  @override
  set taskNote(String? value) => RealmObjectBase.set(this, 'taskNote', value);

  @override
  bool? get disallowReschedule =>
      RealmObjectBase.get<bool>(this, 'disallowReschedule') as bool?;
  @override
  set disallowReschedule(bool? value) =>
      RealmObjectBase.set(this, 'disallowReschedule', value);

  @override
  int? get photoResPerc =>
      RealmObjectBase.get<int>(this, 'photoResPerc') as int?;
  @override
  set photoResPerc(int? value) =>
      RealmObjectBase.set(this, 'photoResPerc', value);

  @override
  bool? get liveImagesOnly =>
      RealmObjectBase.get<bool>(this, 'liveImagesOnly') as bool?;
  @override
  set liveImagesOnly(bool? value) =>
      RealmObjectBase.set(this, 'liveImagesOnly', value);

  @override
  String? get timeSchedule =>
      RealmObjectBase.get<String>(this, 'timeSchedule') as String?;
  @override
  set timeSchedule(String? value) =>
      RealmObjectBase.set(this, 'timeSchedule', value);

  @override
  int? get scheduleTypeId =>
      RealmObjectBase.get<int>(this, 'scheduleTypeId') as int?;
  @override
  set scheduleTypeId(int? value) =>
      RealmObjectBase.set(this, 'scheduleTypeId', value);

  @override
  bool? get showFollowupIconMulti =>
      RealmObjectBase.get<bool>(this, 'showFollowupIconMulti') as bool?;
  @override
  set showFollowupIconMulti(bool? value) =>
      RealmObjectBase.set(this, 'showFollowupIconMulti', value);

  @override
  bool? get followupSelectedMulti =>
      RealmObjectBase.get<bool>(this, 'followupSelectedMulti') as bool?;
  @override
  set followupSelectedMulti(bool? value) =>
      RealmObjectBase.set(this, 'followupSelectedMulti', value);

  @override
  int? get regionId => RealmObjectBase.get<int>(this, 'regionId') as int?;
  @override
  set regionId(int? value) => RealmObjectBase.set(this, 'regionId', value);

  @override
  bool? get isOpen => RealmObjectBase.get<bool>(this, 'isOpen') as bool?;
  @override
  set isOpen(bool? value) => RealmObjectBase.set(this, 'isOpen', value);

  @override
  int? get taskCount => RealmObjectBase.get<int>(this, 'taskCount') as int?;
  @override
  set taskCount(int? value) => RealmObjectBase.set(this, 'taskCount', value);

  @override
  int? get ctFormsTotalCnt =>
      RealmObjectBase.get<int>(this, 'ctFormsTotalCnt') as int?;
  @override
  set ctFormsTotalCnt(int? value) =>
      RealmObjectBase.set(this, 'ctFormsTotalCnt', value);

  @override
  int? get ctFormsCompletedCnt =>
      RealmObjectBase.get<int>(this, 'ctFormsCompletedCnt') as int?;
  @override
  set ctFormsCompletedCnt(int? value) =>
      RealmObjectBase.set(this, 'ctFormsCompletedCnt', value);

  @override
  String? get preftime =>
      RealmObjectBase.get<String>(this, 'preftime') as String?;
  @override
  set preftime(String? value) => RealmObjectBase.set(this, 'preftime', value);

  @override
  bool? get sendTo => RealmObjectBase.get<bool>(this, 'sendTo') as bool?;
  @override
  set sendTo(bool? value) => RealmObjectBase.set(this, 'sendTo', value);

  @override
  int? get kTotal => RealmObjectBase.get<int>(this, 'kTotal') as int?;
  @override
  set kTotal(int? value) => RealmObjectBase.set(this, 'kTotal', value);

  @override
  int? get kCompleted => RealmObjectBase.get<int>(this, 'kCompleted') as int?;
  @override
  set kCompleted(int? value) => RealmObjectBase.set(this, 'kCompleted', value);

  @override
  Stream<RealmObjectChanges<TaskDetailModel>> get changes =>
      RealmObjectBase.getChanges<TaskDetailModel>(this);

  @override
  Stream<RealmObjectChanges<TaskDetailModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<TaskDetailModel>(this, keyPaths);

  @override
  TaskDetailModel freeze() =>
      RealmObjectBase.freezeObject<TaskDetailModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'id': id.toEJson(),
      'isSynced': isSynced.toEJson(),
      'taskId': taskId.toEJson(),
      'projectId': projectId.toEJson(),
      'scheduleId': scheduleId.toEJson(),
      'sentToPayroll': sentToPayroll.toEJson(),
      'showKm': showKm.toEJson(),
      'storeId': storeId.toEJson(),
      'client': client.toEJson(),
      'clientId': clientId.toEJson(),
      'clientLogoUrl': clientLogoUrl.toEJson(),
      'storeGroup': storeGroup.toEJson(),
      'storeGroupId': storeGroupId.toEJson(),
      'storeName': storeName.toEJson(),
      'storeEmail': storeEmail.toEJson(),
      'minutes': minutes.toEJson(),
      'budget': budget.toEJson(),
      'originalbudget': originalbudget.toEJson(),
      'comment': comment.toEJson(),
      'claimableKms': claimableKms.toEJson(),
      'flightDuration': flightDuration.toEJson(),
      'pages': pages.toEJson(),
      'location': location.toEJson(),
      'suburb': suburb.toEJson(),
      'latitude': latitude.toEJson(),
      'longitude': longitude.toEJson(),
      'taskLatitude': taskLatitude.toEJson(),
      'taskLongitude': taskLongitude.toEJson(),
      'cycle': cycle.toEJson(),
      'cycleId': cycleId.toEJson(),
      'canDelete': canDelete.toEJson(),
      'scheduledTimeStamp': scheduledTimeStamp.toEJson(),
      'submissionTimeStamp': submissionTimeStamp.toEJson(),
      'expires': expires.toEJson(),
      'onTask': onTask.toEJson(),
      'phone': phone.toEJson(),
      'rangeStart': rangeStart.toEJson(),
      'rangeEnd': rangeEnd.toEJson(),
      'reOpened': reOpened.toEJson(),
      'reOpenedReason': reOpenedReason.toEJson(),
      'taskStatus': taskStatus.toEJson(),
      'warehousejobId': warehousejobId.toEJson(),
      'connoteUrl': connoteUrl.toEJson(),
      'posRequired': posRequired.toEJson(),
      'isPosMandatory': isPosMandatory.toEJson(),
      'posReceived': posReceived.toEJson(),
      'photoFolder': photoFolder.toEJson(),
      'signatureFolder': signatureFolder.toEJson(),
      'forms': forms.toEJson(),
      'posItems': posItems.toEJson(),
      'documents': documents.toEJson(),
      'taskalerts': taskalerts.toEJson(),
      'taskmembers': taskmembers.toEJson(),
      'photoTagsTwo': photoTagsTwo.toEJson(),
      'photoTagsThree': photoTagsThree.toEJson(),
      'modifiedTimeStampDocuments': modifiedTimeStampDocuments.toEJson(),
      'modifiedTimeStampForms': modifiedTimeStampForms.toEJson(),
      'modifiedTimeStampMembers': modifiedTimeStampMembers.toEJson(),
      'modifiedTimeStampTask': modifiedTimeStampTask.toEJson(),
      'modifiedTimeStampPhotos': modifiedTimeStampPhotos.toEJson(),
      'modifiedTimeStampSignatures': modifiedTimeStampSignatures.toEJson(),
      'modifiedTimeStampSignaturetypes':
          modifiedTimeStampSignaturetypes.toEJson(),
      'posSentTo': posSentTo.toEJson(),
      'posSentToEmail': posSentToEmail.toEJson(),
      'modifiedTimeStampPhototypes': modifiedTimeStampPhototypes.toEJson(),
      'taskCommencementTimeStamp': taskCommencementTimeStamp.toEJson(),
      'taskStoppedTimeStamp': taskStoppedTimeStamp.toEJson(),
      'teamlead': teamlead.toEJson(),
      'followupTasks': followupTasks.toEJson(),
      'stocktake': stocktake.toEJson(),
      'taskNote': taskNote.toEJson(),
      'disallowReschedule': disallowReschedule.toEJson(),
      'photoResPerc': photoResPerc.toEJson(),
      'liveImagesOnly': liveImagesOnly.toEJson(),
      'timeSchedule': timeSchedule.toEJson(),
      'scheduleTypeId': scheduleTypeId.toEJson(),
      'showFollowupIconMulti': showFollowupIconMulti.toEJson(),
      'followupSelectedMulti': followupSelectedMulti.toEJson(),
      'regionId': regionId.toEJson(),
      'isOpen': isOpen.toEJson(),
      'taskCount': taskCount.toEJson(),
      'ctFormsTotalCnt': ctFormsTotalCnt.toEJson(),
      'ctFormsCompletedCnt': ctFormsCompletedCnt.toEJson(),
      'preftime': preftime.toEJson(),
      'sendTo': sendTo.toEJson(),
      'kTotal': kTotal.toEJson(),
      'kCompleted': kCompleted.toEJson(),
    };
  }

  static EJsonValue _toEJson(TaskDetailModel value) => value.toEJson();
  static TaskDetailModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'id': EJsonValue id,
      } =>
        TaskDetailModel(
          fromEJson(ejson['id'], defaultValue: 0),
          isSynced: fromEJson(ejson['isSynced'], defaultValue: false),
          taskId: fromEJson(ejson['taskId']),
          projectId: fromEJson(ejson['projectId']),
          scheduleId: fromEJson(ejson['scheduleId']),
          sentToPayroll: fromEJson(ejson['sentToPayroll']),
          showKm: fromEJson(ejson['showKm']),
          storeId: fromEJson(ejson['storeId']),
          client: fromEJson(ejson['client']),
          clientId: fromEJson(ejson['clientId']),
          clientLogoUrl: fromEJson(ejson['clientLogoUrl']),
          storeGroup: fromEJson(ejson['storeGroup']),
          storeGroupId: fromEJson(ejson['storeGroupId']),
          storeName: fromEJson(ejson['storeName']),
          storeEmail: fromEJson(ejson['storeEmail']),
          minutes: fromEJson(ejson['minutes']),
          budget: fromEJson(ejson['budget']),
          originalbudget: fromEJson(ejson['originalbudget']),
          comment: fromEJson(ejson['comment']),
          claimableKms: fromEJson(ejson['claimableKms']),
          flightDuration: fromEJson(ejson['flightDuration']),
          pages: fromEJson(ejson['pages']),
          location: fromEJson(ejson['location']),
          suburb: fromEJson(ejson['suburb']),
          latitude: fromEJson(ejson['latitude']),
          longitude: fromEJson(ejson['longitude']),
          taskLatitude: fromEJson(ejson['taskLatitude']),
          taskLongitude: fromEJson(ejson['taskLongitude']),
          cycle: fromEJson(ejson['cycle']),
          cycleId: fromEJson(ejson['cycleId']),
          canDelete: fromEJson(ejson['canDelete']),
          scheduledTimeStamp: fromEJson(ejson['scheduledTimeStamp']),
          submissionTimeStamp: fromEJson(ejson['submissionTimeStamp']),
          expires: fromEJson(ejson['expires']),
          onTask: fromEJson(ejson['onTask']),
          phone: fromEJson(ejson['phone']),
          rangeStart: fromEJson(ejson['rangeStart']),
          rangeEnd: fromEJson(ejson['rangeEnd']),
          reOpened: fromEJson(ejson['reOpened']),
          reOpenedReason: fromEJson(ejson['reOpenedReason']),
          taskStatus: fromEJson(ejson['taskStatus']),
          warehousejobId: fromEJson(ejson['warehousejobId']),
          connoteUrl: fromEJson(ejson['connoteUrl']),
          posRequired: fromEJson(ejson['posRequired']),
          isPosMandatory: fromEJson(ejson['isPosMandatory']),
          posReceived: fromEJson(ejson['posReceived']),
          photoFolder: fromEJson(ejson['photoFolder']),
          signatureFolder: fromEJson(ejson['signatureFolder']),
          forms: fromEJson(ejson['forms']),
          posItems: fromEJson(ejson['posItems']),
          documents: fromEJson(ejson['documents']),
          taskalerts: fromEJson(ejson['taskalerts']),
          taskmembers: fromEJson(ejson['taskmembers']),
          photoTagsTwo: fromEJson(ejson['photoTagsTwo']),
          photoTagsThree: fromEJson(ejson['photoTagsThree']),
          modifiedTimeStampDocuments:
              fromEJson(ejson['modifiedTimeStampDocuments']),
          modifiedTimeStampForms: fromEJson(ejson['modifiedTimeStampForms']),
          modifiedTimeStampMembers:
              fromEJson(ejson['modifiedTimeStampMembers']),
          modifiedTimeStampTask: fromEJson(ejson['modifiedTimeStampTask']),
          modifiedTimeStampPhotos: fromEJson(ejson['modifiedTimeStampPhotos']),
          modifiedTimeStampSignatures:
              fromEJson(ejson['modifiedTimeStampSignatures']),
          modifiedTimeStampSignaturetypes:
              fromEJson(ejson['modifiedTimeStampSignaturetypes']),
          posSentTo: fromEJson(ejson['posSentTo']),
          posSentToEmail: fromEJson(ejson['posSentToEmail']),
          modifiedTimeStampPhototypes:
              fromEJson(ejson['modifiedTimeStampPhototypes']),
          taskCommencementTimeStamp:
              fromEJson(ejson['taskCommencementTimeStamp']),
          taskStoppedTimeStamp: fromEJson(ejson['taskStoppedTimeStamp']),
          teamlead: fromEJson(ejson['teamlead']),
          followupTasks: fromEJson(ejson['followupTasks']),
          stocktake: fromEJson(ejson['stocktake']),
          taskNote: fromEJson(ejson['taskNote']),
          disallowReschedule: fromEJson(ejson['disallowReschedule']),
          photoResPerc: fromEJson(ejson['photoResPerc']),
          liveImagesOnly: fromEJson(ejson['liveImagesOnly']),
          timeSchedule: fromEJson(ejson['timeSchedule']),
          scheduleTypeId: fromEJson(ejson['scheduleTypeId']),
          showFollowupIconMulti: fromEJson(ejson['showFollowupIconMulti']),
          followupSelectedMulti: fromEJson(ejson['followupSelectedMulti']),
          regionId: fromEJson(ejson['regionId']),
          isOpen: fromEJson(ejson['isOpen']),
          taskCount: fromEJson(ejson['taskCount']),
          ctFormsTotalCnt: fromEJson(ejson['ctFormsTotalCnt']),
          ctFormsCompletedCnt: fromEJson(ejson['ctFormsCompletedCnt']),
          preftime: fromEJson(ejson['preftime']),
          sendTo: fromEJson(ejson['sendTo']),
          kTotal: fromEJson(ejson['kTotal']),
          kCompleted: fromEJson(ejson['kCompleted']),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(TaskDetailModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, TaskDetailModel, 'TaskDetailModel', [
      SchemaProperty('id', RealmPropertyType.int, primaryKey: true),
      SchemaProperty('isSynced', RealmPropertyType.bool),
      SchemaProperty('taskId', RealmPropertyType.int, optional: true),
      SchemaProperty('projectId', RealmPropertyType.int, optional: true),
      SchemaProperty('scheduleId', RealmPropertyType.int, optional: true),
      SchemaProperty('sentToPayroll', RealmPropertyType.bool, optional: true),
      SchemaProperty('showKm', RealmPropertyType.bool, optional: true),
      SchemaProperty('storeId', RealmPropertyType.int, optional: true),
      SchemaProperty('client', RealmPropertyType.string, optional: true),
      SchemaProperty('clientId', RealmPropertyType.int, optional: true),
      SchemaProperty('clientLogoUrl', RealmPropertyType.string, optional: true),
      SchemaProperty('storeGroup', RealmPropertyType.string, optional: true),
      SchemaProperty('storeGroupId', RealmPropertyType.int, optional: true),
      SchemaProperty('storeName', RealmPropertyType.string, optional: true),
      SchemaProperty('storeEmail', RealmPropertyType.string, optional: true),
      SchemaProperty('minutes', RealmPropertyType.int, optional: true),
      SchemaProperty('budget', RealmPropertyType.int, optional: true),
      SchemaProperty('originalbudget', RealmPropertyType.int, optional: true),
      SchemaProperty('comment', RealmPropertyType.string, optional: true),
      SchemaProperty('claimableKms', RealmPropertyType.int, optional: true),
      SchemaProperty('flightDuration', RealmPropertyType.int, optional: true),
      SchemaProperty('pages', RealmPropertyType.int, optional: true),
      SchemaProperty('location', RealmPropertyType.string, optional: true),
      SchemaProperty('suburb', RealmPropertyType.string, optional: true),
      SchemaProperty('latitude', RealmPropertyType.double, optional: true),
      SchemaProperty('longitude', RealmPropertyType.double, optional: true),
      SchemaProperty('taskLatitude', RealmPropertyType.double, optional: true),
      SchemaProperty('taskLongitude', RealmPropertyType.double, optional: true),
      SchemaProperty('cycle', RealmPropertyType.string, optional: true),
      SchemaProperty('cycleId', RealmPropertyType.int, optional: true),
      SchemaProperty('canDelete', RealmPropertyType.bool, optional: true),
      SchemaProperty('scheduledTimeStamp', RealmPropertyType.timestamp,
          optional: true),
      SchemaProperty('submissionTimeStamp', RealmPropertyType.timestamp,
          optional: true),
      SchemaProperty('expires', RealmPropertyType.timestamp, optional: true),
      SchemaProperty('onTask', RealmPropertyType.string, optional: true),
      SchemaProperty('phone', RealmPropertyType.string, optional: true),
      SchemaProperty('rangeStart', RealmPropertyType.timestamp, optional: true),
      SchemaProperty('rangeEnd', RealmPropertyType.timestamp, optional: true),
      SchemaProperty('reOpened', RealmPropertyType.bool, optional: true),
      SchemaProperty('reOpenedReason', RealmPropertyType.string,
          optional: true),
      SchemaProperty('taskStatus', RealmPropertyType.string, optional: true),
      SchemaProperty('warehousejobId', RealmPropertyType.int, optional: true),
      SchemaProperty('connoteUrl', RealmPropertyType.string, optional: true),
      SchemaProperty('posRequired', RealmPropertyType.bool, optional: true),
      SchemaProperty('isPosMandatory', RealmPropertyType.bool, optional: true),
      SchemaProperty('posReceived', RealmPropertyType.string, optional: true),
      SchemaProperty('photoFolder', RealmPropertyType.object,
          linkTarget: 'PhotoFolderModel',
          collectionType: RealmCollectionType.list),
      SchemaProperty('signatureFolder', RealmPropertyType.object,
          linkTarget: 'SignatureFolderModel',
          collectionType: RealmCollectionType.list),
      SchemaProperty('forms', RealmPropertyType.object,
          linkTarget: 'FormModel', collectionType: RealmCollectionType.list),
      SchemaProperty('posItems', RealmPropertyType.object,
          linkTarget: 'PosItemModel', collectionType: RealmCollectionType.list),
      SchemaProperty('documents', RealmPropertyType.object,
          linkTarget: 'DocumentModel',
          collectionType: RealmCollectionType.list),
      SchemaProperty('taskalerts', RealmPropertyType.object,
          linkTarget: 'TaskalertModel',
          collectionType: RealmCollectionType.list),
      SchemaProperty('taskmembers', RealmPropertyType.object,
          linkTarget: 'TaskmemberModel',
          collectionType: RealmCollectionType.list),
      SchemaProperty('photoTagsTwo', RealmPropertyType.object,
          linkTarget: 'PhotoTagsTModel',
          collectionType: RealmCollectionType.list),
      SchemaProperty('photoTagsThree', RealmPropertyType.object,
          linkTarget: 'PhotoTagsTModel',
          collectionType: RealmCollectionType.list),
      SchemaProperty('modifiedTimeStampDocuments', RealmPropertyType.timestamp,
          optional: true),
      SchemaProperty('modifiedTimeStampForms', RealmPropertyType.timestamp,
          optional: true),
      SchemaProperty('modifiedTimeStampMembers', RealmPropertyType.timestamp,
          optional: true),
      SchemaProperty('modifiedTimeStampTask', RealmPropertyType.timestamp,
          optional: true),
      SchemaProperty('modifiedTimeStampPhotos', RealmPropertyType.timestamp,
          optional: true),
      SchemaProperty('modifiedTimeStampSignatures', RealmPropertyType.timestamp,
          optional: true),
      SchemaProperty(
          'modifiedTimeStampSignaturetypes', RealmPropertyType.timestamp,
          optional: true),
      SchemaProperty('posSentTo', RealmPropertyType.string, optional: true),
      SchemaProperty('posSentToEmail', RealmPropertyType.string,
          optional: true),
      SchemaProperty('modifiedTimeStampPhototypes', RealmPropertyType.timestamp,
          optional: true),
      SchemaProperty('taskCommencementTimeStamp', RealmPropertyType.timestamp,
          optional: true),
      SchemaProperty('taskStoppedTimeStamp', RealmPropertyType.timestamp,
          optional: true),
      SchemaProperty('teamlead', RealmPropertyType.int, optional: true),
      SchemaProperty('followupTasks', RealmPropertyType.object,
          linkTarget: 'FollowupTaskModel',
          collectionType: RealmCollectionType.list),
      SchemaProperty('stocktake', RealmPropertyType.object,
          linkTarget: 'StocktakeModel',
          collectionType: RealmCollectionType.list),
      SchemaProperty('taskNote', RealmPropertyType.string, optional: true),
      SchemaProperty('disallowReschedule', RealmPropertyType.bool,
          optional: true),
      SchemaProperty('photoResPerc', RealmPropertyType.int, optional: true),
      SchemaProperty('liveImagesOnly', RealmPropertyType.bool, optional: true),
      SchemaProperty('timeSchedule', RealmPropertyType.string, optional: true),
      SchemaProperty('scheduleTypeId', RealmPropertyType.int, optional: true),
      SchemaProperty('showFollowupIconMulti', RealmPropertyType.bool,
          optional: true),
      SchemaProperty('followupSelectedMulti', RealmPropertyType.bool,
          optional: true),
      SchemaProperty('regionId', RealmPropertyType.int, optional: true),
      SchemaProperty('isOpen', RealmPropertyType.bool, optional: true),
      SchemaProperty('taskCount', RealmPropertyType.int, optional: true),
      SchemaProperty('ctFormsTotalCnt', RealmPropertyType.int, optional: true),
      SchemaProperty('ctFormsCompletedCnt', RealmPropertyType.int,
          optional: true),
      SchemaProperty('preftime', RealmPropertyType.string, optional: true),
      SchemaProperty('sendTo', RealmPropertyType.bool, optional: true),
      SchemaProperty('kTotal', RealmPropertyType.int, optional: true),
      SchemaProperty('kCompleted', RealmPropertyType.int, optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class DocumentModel extends _DocumentModel
    with RealmEntity, RealmObjectBase, RealmObject {
  DocumentModel({
    int? projectId,
    int? documentId,
    int? documentTypeId,
    String? documentName,
    String? documentIconLink,
    Iterable<FileElementModel> files = const [],
    DateTime? modifiedTimeStampDocument,
  }) {
    RealmObjectBase.set(this, 'projectId', projectId);
    RealmObjectBase.set(this, 'documentId', documentId);
    RealmObjectBase.set(this, 'documentTypeId', documentTypeId);
    RealmObjectBase.set(this, 'documentName', documentName);
    RealmObjectBase.set(this, 'documentIconLink', documentIconLink);
    RealmObjectBase.set<RealmList<FileElementModel>>(
        this, 'files', RealmList<FileElementModel>(files));
    RealmObjectBase.set(
        this, 'modifiedTimeStampDocument', modifiedTimeStampDocument);
  }

  DocumentModel._();

  @override
  int? get projectId => RealmObjectBase.get<int>(this, 'projectId') as int?;
  @override
  set projectId(int? value) => RealmObjectBase.set(this, 'projectId', value);

  @override
  int? get documentId => RealmObjectBase.get<int>(this, 'documentId') as int?;
  @override
  set documentId(int? value) => RealmObjectBase.set(this, 'documentId', value);

  @override
  int? get documentTypeId =>
      RealmObjectBase.get<int>(this, 'documentTypeId') as int?;
  @override
  set documentTypeId(int? value) =>
      RealmObjectBase.set(this, 'documentTypeId', value);

  @override
  String? get documentName =>
      RealmObjectBase.get<String>(this, 'documentName') as String?;
  @override
  set documentName(String? value) =>
      RealmObjectBase.set(this, 'documentName', value);

  @override
  String? get documentIconLink =>
      RealmObjectBase.get<String>(this, 'documentIconLink') as String?;
  @override
  set documentIconLink(String? value) =>
      RealmObjectBase.set(this, 'documentIconLink', value);

  @override
  RealmList<FileElementModel> get files =>
      RealmObjectBase.get<FileElementModel>(this, 'files')
          as RealmList<FileElementModel>;
  @override
  set files(covariant RealmList<FileElementModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  DateTime? get modifiedTimeStampDocument =>
      RealmObjectBase.get<DateTime>(this, 'modifiedTimeStampDocument')
          as DateTime?;
  @override
  set modifiedTimeStampDocument(DateTime? value) =>
      RealmObjectBase.set(this, 'modifiedTimeStampDocument', value);

  @override
  Stream<RealmObjectChanges<DocumentModel>> get changes =>
      RealmObjectBase.getChanges<DocumentModel>(this);

  @override
  Stream<RealmObjectChanges<DocumentModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<DocumentModel>(this, keyPaths);

  @override
  DocumentModel freeze() => RealmObjectBase.freezeObject<DocumentModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'projectId': projectId.toEJson(),
      'documentId': documentId.toEJson(),
      'documentTypeId': documentTypeId.toEJson(),
      'documentName': documentName.toEJson(),
      'documentIconLink': documentIconLink.toEJson(),
      'files': files.toEJson(),
      'modifiedTimeStampDocument': modifiedTimeStampDocument.toEJson(),
    };
  }

  static EJsonValue _toEJson(DocumentModel value) => value.toEJson();
  static DocumentModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return DocumentModel(
      projectId: fromEJson(ejson['projectId']),
      documentId: fromEJson(ejson['documentId']),
      documentTypeId: fromEJson(ejson['documentTypeId']),
      documentName: fromEJson(ejson['documentName']),
      documentIconLink: fromEJson(ejson['documentIconLink']),
      files: fromEJson(ejson['files']),
      modifiedTimeStampDocument: fromEJson(ejson['modifiedTimeStampDocument']),
    );
  }

  static final schema = () {
    RealmObjectBase.registerFactory(DocumentModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, DocumentModel, 'DocumentModel', [
      SchemaProperty('projectId', RealmPropertyType.int, optional: true),
      SchemaProperty('documentId', RealmPropertyType.int, optional: true),
      SchemaProperty('documentTypeId', RealmPropertyType.int, optional: true),
      SchemaProperty('documentName', RealmPropertyType.string, optional: true),
      SchemaProperty('documentIconLink', RealmPropertyType.string,
          optional: true),
      SchemaProperty('files', RealmPropertyType.object,
          linkTarget: 'FileElementModel',
          collectionType: RealmCollectionType.list),
      SchemaProperty('modifiedTimeStampDocument', RealmPropertyType.timestamp,
          optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class FileElementModel extends _FileElementModel
    with RealmEntity, RealmObjectBase, RealmObject {
  FileElementModel({
    int? documentId,
    int? projectId,
    String? documentFileLink,
    int? fileId,
    DateTime? modifiedTimeStampFile,
  }) {
    RealmObjectBase.set(this, 'documentId', documentId);
    RealmObjectBase.set(this, 'projectId', projectId);
    RealmObjectBase.set(this, 'documentFileLink', documentFileLink);
    RealmObjectBase.set(this, 'fileId', fileId);
    RealmObjectBase.set(this, 'modifiedTimeStampFile', modifiedTimeStampFile);
  }

  FileElementModel._();

  @override
  int? get documentId => RealmObjectBase.get<int>(this, 'documentId') as int?;
  @override
  set documentId(int? value) => RealmObjectBase.set(this, 'documentId', value);

  @override
  int? get projectId => RealmObjectBase.get<int>(this, 'projectId') as int?;
  @override
  set projectId(int? value) => RealmObjectBase.set(this, 'projectId', value);

  @override
  String? get documentFileLink =>
      RealmObjectBase.get<String>(this, 'documentFileLink') as String?;
  @override
  set documentFileLink(String? value) =>
      RealmObjectBase.set(this, 'documentFileLink', value);

  @override
  int? get fileId => RealmObjectBase.get<int>(this, 'fileId') as int?;
  @override
  set fileId(int? value) => RealmObjectBase.set(this, 'fileId', value);

  @override
  DateTime? get modifiedTimeStampFile =>
      RealmObjectBase.get<DateTime>(this, 'modifiedTimeStampFile') as DateTime?;
  @override
  set modifiedTimeStampFile(DateTime? value) =>
      RealmObjectBase.set(this, 'modifiedTimeStampFile', value);

  @override
  Stream<RealmObjectChanges<FileElementModel>> get changes =>
      RealmObjectBase.getChanges<FileElementModel>(this);

  @override
  Stream<RealmObjectChanges<FileElementModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<FileElementModel>(this, keyPaths);

  @override
  FileElementModel freeze() =>
      RealmObjectBase.freezeObject<FileElementModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'documentId': documentId.toEJson(),
      'projectId': projectId.toEJson(),
      'documentFileLink': documentFileLink.toEJson(),
      'fileId': fileId.toEJson(),
      'modifiedTimeStampFile': modifiedTimeStampFile.toEJson(),
    };
  }

  static EJsonValue _toEJson(FileElementModel value) => value.toEJson();
  static FileElementModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return FileElementModel(
      documentId: fromEJson(ejson['documentId']),
      projectId: fromEJson(ejson['projectId']),
      documentFileLink: fromEJson(ejson['documentFileLink']),
      fileId: fromEJson(ejson['fileId']),
      modifiedTimeStampFile: fromEJson(ejson['modifiedTimeStampFile']),
    );
  }

  static final schema = () {
    RealmObjectBase.registerFactory(FileElementModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, FileElementModel, 'FileElementModel', [
      SchemaProperty('documentId', RealmPropertyType.int, optional: true),
      SchemaProperty('projectId', RealmPropertyType.int, optional: true),
      SchemaProperty('documentFileLink', RealmPropertyType.string,
          optional: true),
      SchemaProperty('fileId', RealmPropertyType.int, optional: true),
      SchemaProperty('modifiedTimeStampFile', RealmPropertyType.timestamp,
          optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class FollowupTaskModel extends _FollowupTaskModel
    with RealmEntity, RealmObjectBase, RealmObject {
  FollowupTaskModel({
    bool? showFollowupIcon,
    bool? followupSelected,
    DateTime? selectedVisitDate,
    int? selectedFollowupTypeId,
    int? selectedFollowupItemId,
    int? selectedBudget,
    String? selectedFollowupType,
    String? selectedFollowupItem,
    String? selectedScheduleNote,
    int? followupNumber,
  }) {
    RealmObjectBase.set(this, 'showFollowupIcon', showFollowupIcon);
    RealmObjectBase.set(this, 'followupSelected', followupSelected);
    RealmObjectBase.set(this, 'selectedVisitDate', selectedVisitDate);
    RealmObjectBase.set(this, 'selectedFollowupTypeId', selectedFollowupTypeId);
    RealmObjectBase.set(this, 'selectedFollowupItemId', selectedFollowupItemId);
    RealmObjectBase.set(this, 'selectedBudget', selectedBudget);
    RealmObjectBase.set(this, 'selectedFollowupType', selectedFollowupType);
    RealmObjectBase.set(this, 'selectedFollowupItem', selectedFollowupItem);
    RealmObjectBase.set(this, 'selectedScheduleNote', selectedScheduleNote);
    RealmObjectBase.set(this, 'followupNumber', followupNumber);
  }

  FollowupTaskModel._();

  @override
  bool? get showFollowupIcon =>
      RealmObjectBase.get<bool>(this, 'showFollowupIcon') as bool?;
  @override
  set showFollowupIcon(bool? value) =>
      RealmObjectBase.set(this, 'showFollowupIcon', value);

  @override
  bool? get followupSelected =>
      RealmObjectBase.get<bool>(this, 'followupSelected') as bool?;
  @override
  set followupSelected(bool? value) =>
      RealmObjectBase.set(this, 'followupSelected', value);

  @override
  DateTime? get selectedVisitDate =>
      RealmObjectBase.get<DateTime>(this, 'selectedVisitDate') as DateTime?;
  @override
  set selectedVisitDate(DateTime? value) =>
      RealmObjectBase.set(this, 'selectedVisitDate', value);

  @override
  int? get selectedFollowupTypeId =>
      RealmObjectBase.get<int>(this, 'selectedFollowupTypeId') as int?;
  @override
  set selectedFollowupTypeId(int? value) =>
      RealmObjectBase.set(this, 'selectedFollowupTypeId', value);

  @override
  int? get selectedFollowupItemId =>
      RealmObjectBase.get<int>(this, 'selectedFollowupItemId') as int?;
  @override
  set selectedFollowupItemId(int? value) =>
      RealmObjectBase.set(this, 'selectedFollowupItemId', value);

  @override
  int? get selectedBudget =>
      RealmObjectBase.get<int>(this, 'selectedBudget') as int?;
  @override
  set selectedBudget(int? value) =>
      RealmObjectBase.set(this, 'selectedBudget', value);

  @override
  String? get selectedFollowupType =>
      RealmObjectBase.get<String>(this, 'selectedFollowupType') as String?;
  @override
  set selectedFollowupType(String? value) =>
      RealmObjectBase.set(this, 'selectedFollowupType', value);

  @override
  String? get selectedFollowupItem =>
      RealmObjectBase.get<String>(this, 'selectedFollowupItem') as String?;
  @override
  set selectedFollowupItem(String? value) =>
      RealmObjectBase.set(this, 'selectedFollowupItem', value);

  @override
  String? get selectedScheduleNote =>
      RealmObjectBase.get<String>(this, 'selectedScheduleNote') as String?;
  @override
  set selectedScheduleNote(String? value) =>
      RealmObjectBase.set(this, 'selectedScheduleNote', value);

  @override
  int? get followupNumber =>
      RealmObjectBase.get<int>(this, 'followupNumber') as int?;
  @override
  set followupNumber(int? value) =>
      RealmObjectBase.set(this, 'followupNumber', value);

  @override
  Stream<RealmObjectChanges<FollowupTaskModel>> get changes =>
      RealmObjectBase.getChanges<FollowupTaskModel>(this);

  @override
  Stream<RealmObjectChanges<FollowupTaskModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<FollowupTaskModel>(this, keyPaths);

  @override
  FollowupTaskModel freeze() =>
      RealmObjectBase.freezeObject<FollowupTaskModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'showFollowupIcon': showFollowupIcon.toEJson(),
      'followupSelected': followupSelected.toEJson(),
      'selectedVisitDate': selectedVisitDate.toEJson(),
      'selectedFollowupTypeId': selectedFollowupTypeId.toEJson(),
      'selectedFollowupItemId': selectedFollowupItemId.toEJson(),
      'selectedBudget': selectedBudget.toEJson(),
      'selectedFollowupType': selectedFollowupType.toEJson(),
      'selectedFollowupItem': selectedFollowupItem.toEJson(),
      'selectedScheduleNote': selectedScheduleNote.toEJson(),
      'followupNumber': followupNumber.toEJson(),
    };
  }

  static EJsonValue _toEJson(FollowupTaskModel value) => value.toEJson();
  static FollowupTaskModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return FollowupTaskModel(
      showFollowupIcon: fromEJson(ejson['showFollowupIcon']),
      followupSelected: fromEJson(ejson['followupSelected']),
      selectedVisitDate: fromEJson(ejson['selectedVisitDate']),
      selectedFollowupTypeId: fromEJson(ejson['selectedFollowupTypeId']),
      selectedFollowupItemId: fromEJson(ejson['selectedFollowupItemId']),
      selectedBudget: fromEJson(ejson['selectedBudget']),
      selectedFollowupType: fromEJson(ejson['selectedFollowupType']),
      selectedFollowupItem: fromEJson(ejson['selectedFollowupItem']),
      selectedScheduleNote: fromEJson(ejson['selectedScheduleNote']),
      followupNumber: fromEJson(ejson['followupNumber']),
    );
  }

  static final schema = () {
    RealmObjectBase.registerFactory(FollowupTaskModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, FollowupTaskModel, 'FollowupTaskModel', [
      SchemaProperty('showFollowupIcon', RealmPropertyType.bool,
          optional: true),
      SchemaProperty('followupSelected', RealmPropertyType.bool,
          optional: true),
      SchemaProperty('selectedVisitDate', RealmPropertyType.timestamp,
          optional: true),
      SchemaProperty('selectedFollowupTypeId', RealmPropertyType.int,
          optional: true),
      SchemaProperty('selectedFollowupItemId', RealmPropertyType.int,
          optional: true),
      SchemaProperty('selectedBudget', RealmPropertyType.int, optional: true),
      SchemaProperty('selectedFollowupType', RealmPropertyType.string,
          optional: true),
      SchemaProperty('selectedFollowupItem', RealmPropertyType.string,
          optional: true),
      SchemaProperty('selectedScheduleNote', RealmPropertyType.string,
          optional: true),
      SchemaProperty('followupNumber', RealmPropertyType.int, optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class FormModel extends _FormModel
    with RealmEntity, RealmObjectBase, RealmObject {
  FormModel({
    int? formId,
    int? formInstanceId,
    String? formName,
    String? briefUrl,
    Iterable<QuestionModel> questions = const [],
    Iterable<QuestionAnswerModel> questionAnswers = const [],
    DateTime? modifiedTimeStampForm,
    DateTime? dateStart,
    bool? isVisionForm,
    String? visionFormUrl,
    bool? isMandatory,
    bool? formPreview,
    int? formTypeId,
    bool? formCompleted,
    bool? formAllowForward,
    bool? formShowPrice,
    int? minQty,
    bool? showQuestions,
    int? kTotal,
    int? kCompleted,
  }) {
    RealmObjectBase.set(this, 'formId', formId);
    RealmObjectBase.set(this, 'formInstanceId', formInstanceId);
    RealmObjectBase.set(this, 'formName', formName);
    RealmObjectBase.set(this, 'briefUrl', briefUrl);
    RealmObjectBase.set<RealmList<QuestionModel>>(
        this, 'questions', RealmList<QuestionModel>(questions));
    RealmObjectBase.set<RealmList<QuestionAnswerModel>>(this, 'questionAnswers',
        RealmList<QuestionAnswerModel>(questionAnswers));
    RealmObjectBase.set(this, 'modifiedTimeStampForm', modifiedTimeStampForm);
    RealmObjectBase.set(this, 'dateStart', dateStart);
    RealmObjectBase.set(this, 'isVisionForm', isVisionForm);
    RealmObjectBase.set(this, 'visionFormUrl', visionFormUrl);
    RealmObjectBase.set(this, 'isMandatory', isMandatory);
    RealmObjectBase.set(this, 'formPreview', formPreview);
    RealmObjectBase.set(this, 'formTypeId', formTypeId);
    RealmObjectBase.set(this, 'formCompleted', formCompleted);
    RealmObjectBase.set(this, 'formAllowForward', formAllowForward);
    RealmObjectBase.set(this, 'formShowPrice', formShowPrice);
    RealmObjectBase.set(this, 'minQty', minQty);
    RealmObjectBase.set(this, 'showQuestions', showQuestions);
    RealmObjectBase.set(this, 'kTotal', kTotal);
    RealmObjectBase.set(this, 'kCompleted', kCompleted);
  }

  FormModel._();

  @override
  int? get formId => RealmObjectBase.get<int>(this, 'formId') as int?;
  @override
  set formId(int? value) => RealmObjectBase.set(this, 'formId', value);

  @override
  int? get formInstanceId =>
      RealmObjectBase.get<int>(this, 'formInstanceId') as int?;
  @override
  set formInstanceId(int? value) =>
      RealmObjectBase.set(this, 'formInstanceId', value);

  @override
  String? get formName =>
      RealmObjectBase.get<String>(this, 'formName') as String?;
  @override
  set formName(String? value) => RealmObjectBase.set(this, 'formName', value);

  @override
  String? get briefUrl =>
      RealmObjectBase.get<String>(this, 'briefUrl') as String?;
  @override
  set briefUrl(String? value) => RealmObjectBase.set(this, 'briefUrl', value);

  @override
  RealmList<QuestionModel> get questions =>
      RealmObjectBase.get<QuestionModel>(this, 'questions')
          as RealmList<QuestionModel>;
  @override
  set questions(covariant RealmList<QuestionModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  RealmList<QuestionAnswerModel> get questionAnswers =>
      RealmObjectBase.get<QuestionAnswerModel>(this, 'questionAnswers')
          as RealmList<QuestionAnswerModel>;
  @override
  set questionAnswers(covariant RealmList<QuestionAnswerModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  DateTime? get modifiedTimeStampForm =>
      RealmObjectBase.get<DateTime>(this, 'modifiedTimeStampForm') as DateTime?;
  @override
  set modifiedTimeStampForm(DateTime? value) =>
      RealmObjectBase.set(this, 'modifiedTimeStampForm', value);

  @override
  DateTime? get dateStart =>
      RealmObjectBase.get<DateTime>(this, 'dateStart') as DateTime?;
  @override
  set dateStart(DateTime? value) =>
      RealmObjectBase.set(this, 'dateStart', value);

  @override
  bool? get isVisionForm =>
      RealmObjectBase.get<bool>(this, 'isVisionForm') as bool?;
  @override
  set isVisionForm(bool? value) =>
      RealmObjectBase.set(this, 'isVisionForm', value);

  @override
  String? get visionFormUrl =>
      RealmObjectBase.get<String>(this, 'visionFormUrl') as String?;
  @override
  set visionFormUrl(String? value) =>
      RealmObjectBase.set(this, 'visionFormUrl', value);

  @override
  bool? get isMandatory =>
      RealmObjectBase.get<bool>(this, 'isMandatory') as bool?;
  @override
  set isMandatory(bool? value) =>
      RealmObjectBase.set(this, 'isMandatory', value);

  @override
  bool? get formPreview =>
      RealmObjectBase.get<bool>(this, 'formPreview') as bool?;
  @override
  set formPreview(bool? value) =>
      RealmObjectBase.set(this, 'formPreview', value);

  @override
  int? get formTypeId => RealmObjectBase.get<int>(this, 'formTypeId') as int?;
  @override
  set formTypeId(int? value) => RealmObjectBase.set(this, 'formTypeId', value);

  @override
  bool? get formCompleted =>
      RealmObjectBase.get<bool>(this, 'formCompleted') as bool?;
  @override
  set formCompleted(bool? value) =>
      RealmObjectBase.set(this, 'formCompleted', value);

  @override
  bool? get formAllowForward =>
      RealmObjectBase.get<bool>(this, 'formAllowForward') as bool?;
  @override
  set formAllowForward(bool? value) =>
      RealmObjectBase.set(this, 'formAllowForward', value);

  @override
  bool? get formShowPrice =>
      RealmObjectBase.get<bool>(this, 'formShowPrice') as bool?;
  @override
  set formShowPrice(bool? value) =>
      RealmObjectBase.set(this, 'formShowPrice', value);

  @override
  int? get minQty => RealmObjectBase.get<int>(this, 'minQty') as int?;
  @override
  set minQty(int? value) => RealmObjectBase.set(this, 'minQty', value);

  @override
  bool? get showQuestions =>
      RealmObjectBase.get<bool>(this, 'showQuestions') as bool?;
  @override
  set showQuestions(bool? value) =>
      RealmObjectBase.set(this, 'showQuestions', value);

  @override
  int? get kTotal => RealmObjectBase.get<int>(this, 'kTotal') as int?;
  @override
  set kTotal(int? value) => RealmObjectBase.set(this, 'kTotal', value);

  @override
  int? get kCompleted => RealmObjectBase.get<int>(this, 'kCompleted') as int?;
  @override
  set kCompleted(int? value) => RealmObjectBase.set(this, 'kCompleted', value);

  @override
  Stream<RealmObjectChanges<FormModel>> get changes =>
      RealmObjectBase.getChanges<FormModel>(this);

  @override
  Stream<RealmObjectChanges<FormModel>> changesFor([List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<FormModel>(this, keyPaths);

  @override
  FormModel freeze() => RealmObjectBase.freezeObject<FormModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'formId': formId.toEJson(),
      'formInstanceId': formInstanceId.toEJson(),
      'formName': formName.toEJson(),
      'briefUrl': briefUrl.toEJson(),
      'questions': questions.toEJson(),
      'questionAnswers': questionAnswers.toEJson(),
      'modifiedTimeStampForm': modifiedTimeStampForm.toEJson(),
      'dateStart': dateStart.toEJson(),
      'isVisionForm': isVisionForm.toEJson(),
      'visionFormUrl': visionFormUrl.toEJson(),
      'isMandatory': isMandatory.toEJson(),
      'formPreview': formPreview.toEJson(),
      'formTypeId': formTypeId.toEJson(),
      'formCompleted': formCompleted.toEJson(),
      'formAllowForward': formAllowForward.toEJson(),
      'formShowPrice': formShowPrice.toEJson(),
      'minQty': minQty.toEJson(),
      'showQuestions': showQuestions.toEJson(),
      'kTotal': kTotal.toEJson(),
      'kCompleted': kCompleted.toEJson(),
    };
  }

  static EJsonValue _toEJson(FormModel value) => value.toEJson();
  static FormModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return FormModel(
      formId: fromEJson(ejson['formId']),
      formInstanceId: fromEJson(ejson['formInstanceId']),
      formName: fromEJson(ejson['formName']),
      briefUrl: fromEJson(ejson['briefUrl']),
      questions: fromEJson(ejson['questions']),
      questionAnswers: fromEJson(ejson['questionAnswers']),
      modifiedTimeStampForm: fromEJson(ejson['modifiedTimeStampForm']),
      dateStart: fromEJson(ejson['dateStart']),
      isVisionForm: fromEJson(ejson['isVisionForm']),
      visionFormUrl: fromEJson(ejson['visionFormUrl']),
      isMandatory: fromEJson(ejson['isMandatory']),
      formPreview: fromEJson(ejson['formPreview']),
      formTypeId: fromEJson(ejson['formTypeId']),
      formCompleted: fromEJson(ejson['formCompleted']),
      formAllowForward: fromEJson(ejson['formAllowForward']),
      formShowPrice: fromEJson(ejson['formShowPrice']),
      minQty: fromEJson(ejson['minQty']),
      showQuestions: fromEJson(ejson['showQuestions']),
      kTotal: fromEJson(ejson['kTotal']),
      kCompleted: fromEJson(ejson['kCompleted']),
    );
  }

  static final schema = () {
    RealmObjectBase.registerFactory(FormModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(ObjectType.realmObject, FormModel, 'FormModel', [
      SchemaProperty('formId', RealmPropertyType.int, optional: true),
      SchemaProperty('formInstanceId', RealmPropertyType.int, optional: true),
      SchemaProperty('formName', RealmPropertyType.string, optional: true),
      SchemaProperty('briefUrl', RealmPropertyType.string, optional: true),
      SchemaProperty('questions', RealmPropertyType.object,
          linkTarget: 'QuestionModel',
          collectionType: RealmCollectionType.list),
      SchemaProperty('questionAnswers', RealmPropertyType.object,
          linkTarget: 'QuestionAnswerModel',
          collectionType: RealmCollectionType.list),
      SchemaProperty('modifiedTimeStampForm', RealmPropertyType.timestamp,
          optional: true),
      SchemaProperty('dateStart', RealmPropertyType.timestamp, optional: true),
      SchemaProperty('isVisionForm', RealmPropertyType.bool, optional: true),
      SchemaProperty('visionFormUrl', RealmPropertyType.string, optional: true),
      SchemaProperty('isMandatory', RealmPropertyType.bool, optional: true),
      SchemaProperty('formPreview', RealmPropertyType.bool, optional: true),
      SchemaProperty('formTypeId', RealmPropertyType.int, optional: true),
      SchemaProperty('formCompleted', RealmPropertyType.bool, optional: true),
      SchemaProperty('formAllowForward', RealmPropertyType.bool,
          optional: true),
      SchemaProperty('formShowPrice', RealmPropertyType.bool, optional: true),
      SchemaProperty('minQty', RealmPropertyType.int, optional: true),
      SchemaProperty('showQuestions', RealmPropertyType.bool, optional: true),
      SchemaProperty('kTotal', RealmPropertyType.int, optional: true),
      SchemaProperty('kCompleted', RealmPropertyType.int, optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class QuestionAnswerModel extends _QuestionAnswerModel
    with RealmEntity, RealmObjectBase, RealmObject {
  QuestionAnswerModel({
    int? taskId,
    int? formId,
    int? questionId,
    int? questionpartId,
    bool? flip,
    String? questionPartMultiId,
    int? measurementId,
    int? measurementTypeId,
    int? measurementOptionId,
    String? measurementOptionIds,
    String? measurementTextResult,
    bool? isComment,
    int? commentTypeId,
  }) {
    RealmObjectBase.set(this, 'taskId', taskId);
    RealmObjectBase.set(this, 'formId', formId);
    RealmObjectBase.set(this, 'questionId', questionId);
    RealmObjectBase.set(this, 'questionpartId', questionpartId);
    RealmObjectBase.set(this, 'flip', flip);
    RealmObjectBase.set(this, 'questionPartMultiId', questionPartMultiId);
    RealmObjectBase.set(this, 'measurementId', measurementId);
    RealmObjectBase.set(this, 'measurementTypeId', measurementTypeId);
    RealmObjectBase.set(this, 'measurementOptionId', measurementOptionId);
    RealmObjectBase.set(this, 'measurementOptionIds', measurementOptionIds);
    RealmObjectBase.set(this, 'measurementTextResult', measurementTextResult);
    RealmObjectBase.set(this, 'isComment', isComment);
    RealmObjectBase.set(this, 'commentTypeId', commentTypeId);
  }

  QuestionAnswerModel._();

  @override
  int? get taskId => RealmObjectBase.get<int>(this, 'taskId') as int?;
  @override
  set taskId(int? value) => RealmObjectBase.set(this, 'taskId', value);

  @override
  int? get formId => RealmObjectBase.get<int>(this, 'formId') as int?;
  @override
  set formId(int? value) => RealmObjectBase.set(this, 'formId', value);

  @override
  int? get questionId => RealmObjectBase.get<int>(this, 'questionId') as int?;
  @override
  set questionId(int? value) => RealmObjectBase.set(this, 'questionId', value);

  @override
  int? get questionpartId =>
      RealmObjectBase.get<int>(this, 'questionpartId') as int?;
  @override
  set questionpartId(int? value) =>
      RealmObjectBase.set(this, 'questionpartId', value);

  @override
  bool? get flip => RealmObjectBase.get<bool>(this, 'flip') as bool?;
  @override
  set flip(bool? value) => RealmObjectBase.set(this, 'flip', value);

  @override
  String? get questionPartMultiId =>
      RealmObjectBase.get<String>(this, 'questionPartMultiId') as String?;
  @override
  set questionPartMultiId(String? value) =>
      RealmObjectBase.set(this, 'questionPartMultiId', value);

  @override
  int? get measurementId =>
      RealmObjectBase.get<int>(this, 'measurementId') as int?;
  @override
  set measurementId(int? value) =>
      RealmObjectBase.set(this, 'measurementId', value);

  @override
  int? get measurementTypeId =>
      RealmObjectBase.get<int>(this, 'measurementTypeId') as int?;
  @override
  set measurementTypeId(int? value) =>
      RealmObjectBase.set(this, 'measurementTypeId', value);

  @override
  int? get measurementOptionId =>
      RealmObjectBase.get<int>(this, 'measurementOptionId') as int?;
  @override
  set measurementOptionId(int? value) =>
      RealmObjectBase.set(this, 'measurementOptionId', value);

  @override
  String? get measurementOptionIds =>
      RealmObjectBase.get<String>(this, 'measurementOptionIds') as String?;
  @override
  set measurementOptionIds(String? value) =>
      RealmObjectBase.set(this, 'measurementOptionIds', value);

  @override
  String? get measurementTextResult =>
      RealmObjectBase.get<String>(this, 'measurementTextResult') as String?;
  @override
  set measurementTextResult(String? value) =>
      RealmObjectBase.set(this, 'measurementTextResult', value);

  @override
  bool? get isComment => RealmObjectBase.get<bool>(this, 'isComment') as bool?;
  @override
  set isComment(bool? value) => RealmObjectBase.set(this, 'isComment', value);

  @override
  int? get commentTypeId =>
      RealmObjectBase.get<int>(this, 'commentTypeId') as int?;
  @override
  set commentTypeId(int? value) =>
      RealmObjectBase.set(this, 'commentTypeId', value);

  @override
  Stream<RealmObjectChanges<QuestionAnswerModel>> get changes =>
      RealmObjectBase.getChanges<QuestionAnswerModel>(this);

  @override
  Stream<RealmObjectChanges<QuestionAnswerModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<QuestionAnswerModel>(this, keyPaths);

  @override
  QuestionAnswerModel freeze() =>
      RealmObjectBase.freezeObject<QuestionAnswerModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'taskId': taskId.toEJson(),
      'formId': formId.toEJson(),
      'questionId': questionId.toEJson(),
      'questionpartId': questionpartId.toEJson(),
      'flip': flip.toEJson(),
      'questionPartMultiId': questionPartMultiId.toEJson(),
      'measurementId': measurementId.toEJson(),
      'measurementTypeId': measurementTypeId.toEJson(),
      'measurementOptionId': measurementOptionId.toEJson(),
      'measurementOptionIds': measurementOptionIds.toEJson(),
      'measurementTextResult': measurementTextResult.toEJson(),
      'isComment': isComment.toEJson(),
      'commentTypeId': commentTypeId.toEJson(),
    };
  }

  static EJsonValue _toEJson(QuestionAnswerModel value) => value.toEJson();
  static QuestionAnswerModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return QuestionAnswerModel(
      taskId: fromEJson(ejson['taskId']),
      formId: fromEJson(ejson['formId']),
      questionId: fromEJson(ejson['questionId']),
      questionpartId: fromEJson(ejson['questionpartId']),
      flip: fromEJson(ejson['flip']),
      questionPartMultiId: fromEJson(ejson['questionPartMultiId']),
      measurementId: fromEJson(ejson['measurementId']),
      measurementTypeId: fromEJson(ejson['measurementTypeId']),
      measurementOptionId: fromEJson(ejson['measurementOptionId']),
      measurementOptionIds: fromEJson(ejson['measurementOptionIds']),
      measurementTextResult: fromEJson(ejson['measurementTextResult']),
      isComment: fromEJson(ejson['isComment']),
      commentTypeId: fromEJson(ejson['commentTypeId']),
    );
  }

  static final schema = () {
    RealmObjectBase.registerFactory(QuestionAnswerModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, QuestionAnswerModel, 'QuestionAnswerModel', [
      SchemaProperty('taskId', RealmPropertyType.int, optional: true),
      SchemaProperty('formId', RealmPropertyType.int, optional: true),
      SchemaProperty('questionId', RealmPropertyType.int, optional: true),
      SchemaProperty('questionpartId', RealmPropertyType.int, optional: true),
      SchemaProperty('flip', RealmPropertyType.bool, optional: true),
      SchemaProperty('questionPartMultiId', RealmPropertyType.string,
          optional: true),
      SchemaProperty('measurementId', RealmPropertyType.int, optional: true),
      SchemaProperty('measurementTypeId', RealmPropertyType.int,
          optional: true),
      SchemaProperty('measurementOptionId', RealmPropertyType.int,
          optional: true),
      SchemaProperty('measurementOptionIds', RealmPropertyType.string,
          optional: true),
      SchemaProperty('measurementTextResult', RealmPropertyType.string,
          optional: true),
      SchemaProperty('isComment', RealmPropertyType.bool, optional: true),
      SchemaProperty('commentTypeId', RealmPropertyType.int, optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class QuestionModel extends _QuestionModel
    with RealmEntity, RealmObjectBase, RealmObject {
  QuestionModel({
    int? questionId,
    int? questionOrderId,
    String? questionDescription,
    bool? isComment,
    bool? isCommentMandatory,
    bool? showQuestions,
    bool? hasSignature,
    bool? isSignatureMandatory,
    String? signatureUrl,
    String? photoUrl,
    String? questionBrief,
    Iterable<QuestionPartModel> questionParts = const [],
    Iterable<MeasurementModel> measurements = const [],
    Iterable<QuestionConditionModel> questionConditions = const [],
    Iterable<CommentTypeModel> commentTypes = const [],
    DateTime? modifiedTimeStampQuestion,
    bool? targetByCycle,
    bool? targetByGroup,
    bool? targetByCompany,
    bool? targetByRegion,
    bool? targetByBudget,
    bool? isMll,
    Iterable<PhotoTagsTModel> photoTagsTwo = const [],
    Iterable<PhotoTagsTModel> photoTagsThree = const [],
    bool? isMulti,
    int? multiMeasurementId,
    bool? isMultiOneAnswer,
    bool? flip,
    int? questionTypeId,
  }) {
    RealmObjectBase.set(this, 'questionId', questionId);
    RealmObjectBase.set(this, 'questionOrderId', questionOrderId);
    RealmObjectBase.set(this, 'questionDescription', questionDescription);
    RealmObjectBase.set(this, 'isComment', isComment);
    RealmObjectBase.set(this, 'isCommentMandatory', isCommentMandatory);
    RealmObjectBase.set(this, 'showQuestions', showQuestions);
    RealmObjectBase.set(this, 'hasSignature', hasSignature);
    RealmObjectBase.set(this, 'isSignatureMandatory', isSignatureMandatory);
    RealmObjectBase.set(this, 'signatureUrl', signatureUrl);
    RealmObjectBase.set(this, 'photoUrl', photoUrl);
    RealmObjectBase.set(this, 'questionBrief', questionBrief);
    RealmObjectBase.set<RealmList<QuestionPartModel>>(
        this, 'questionParts', RealmList<QuestionPartModel>(questionParts));
    RealmObjectBase.set<RealmList<MeasurementModel>>(
        this, 'measurements', RealmList<MeasurementModel>(measurements));
    RealmObjectBase.set<RealmList<QuestionConditionModel>>(
        this,
        'questionConditions',
        RealmList<QuestionConditionModel>(questionConditions));
    RealmObjectBase.set<RealmList<CommentTypeModel>>(
        this, 'commentTypes', RealmList<CommentTypeModel>(commentTypes));
    RealmObjectBase.set(
        this, 'modifiedTimeStampQuestion', modifiedTimeStampQuestion);
    RealmObjectBase.set(this, 'targetByCycle', targetByCycle);
    RealmObjectBase.set(this, 'targetByGroup', targetByGroup);
    RealmObjectBase.set(this, 'targetByCompany', targetByCompany);
    RealmObjectBase.set(this, 'targetByRegion', targetByRegion);
    RealmObjectBase.set(this, 'targetByBudget', targetByBudget);
    RealmObjectBase.set(this, 'isMll', isMll);
    RealmObjectBase.set<RealmList<PhotoTagsTModel>>(
        this, 'photoTagsTwo', RealmList<PhotoTagsTModel>(photoTagsTwo));
    RealmObjectBase.set<RealmList<PhotoTagsTModel>>(
        this, 'photoTagsThree', RealmList<PhotoTagsTModel>(photoTagsThree));
    RealmObjectBase.set(this, 'isMulti', isMulti);
    RealmObjectBase.set(this, 'multiMeasurementId', multiMeasurementId);
    RealmObjectBase.set(this, 'isMultiOneAnswer', isMultiOneAnswer);
    RealmObjectBase.set(this, 'flip', flip);
    RealmObjectBase.set(this, 'questionTypeId', questionTypeId);
  }

  QuestionModel._();

  @override
  int? get questionId => RealmObjectBase.get<int>(this, 'questionId') as int?;
  @override
  set questionId(int? value) => RealmObjectBase.set(this, 'questionId', value);

  @override
  int? get questionOrderId =>
      RealmObjectBase.get<int>(this, 'questionOrderId') as int?;
  @override
  set questionOrderId(int? value) =>
      RealmObjectBase.set(this, 'questionOrderId', value);

  @override
  String? get questionDescription =>
      RealmObjectBase.get<String>(this, 'questionDescription') as String?;
  @override
  set questionDescription(String? value) =>
      RealmObjectBase.set(this, 'questionDescription', value);

  @override
  bool? get isComment => RealmObjectBase.get<bool>(this, 'isComment') as bool?;
  @override
  set isComment(bool? value) => RealmObjectBase.set(this, 'isComment', value);

  @override
  bool? get isCommentMandatory =>
      RealmObjectBase.get<bool>(this, 'isCommentMandatory') as bool?;
  @override
  set isCommentMandatory(bool? value) =>
      RealmObjectBase.set(this, 'isCommentMandatory', value);

  @override
  bool? get showQuestions =>
      RealmObjectBase.get<bool>(this, 'showQuestions') as bool?;
  @override
  set showQuestions(bool? value) =>
      RealmObjectBase.set(this, 'showQuestions', value);

  @override
  bool? get hasSignature =>
      RealmObjectBase.get<bool>(this, 'hasSignature') as bool?;
  @override
  set hasSignature(bool? value) =>
      RealmObjectBase.set(this, 'hasSignature', value);

  @override
  bool? get isSignatureMandatory =>
      RealmObjectBase.get<bool>(this, 'isSignatureMandatory') as bool?;
  @override
  set isSignatureMandatory(bool? value) =>
      RealmObjectBase.set(this, 'isSignatureMandatory', value);

  @override
  String? get signatureUrl =>
      RealmObjectBase.get<String>(this, 'signatureUrl') as String?;
  @override
  set signatureUrl(String? value) =>
      RealmObjectBase.set(this, 'signatureUrl', value);

  @override
  String? get photoUrl =>
      RealmObjectBase.get<String>(this, 'photoUrl') as String?;
  @override
  set photoUrl(String? value) => RealmObjectBase.set(this, 'photoUrl', value);

  @override
  String? get questionBrief =>
      RealmObjectBase.get<String>(this, 'questionBrief') as String?;
  @override
  set questionBrief(String? value) =>
      RealmObjectBase.set(this, 'questionBrief', value);

  @override
  RealmList<QuestionPartModel> get questionParts =>
      RealmObjectBase.get<QuestionPartModel>(this, 'questionParts')
          as RealmList<QuestionPartModel>;
  @override
  set questionParts(covariant RealmList<QuestionPartModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  RealmList<MeasurementModel> get measurements =>
      RealmObjectBase.get<MeasurementModel>(this, 'measurements')
          as RealmList<MeasurementModel>;
  @override
  set measurements(covariant RealmList<MeasurementModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  RealmList<QuestionConditionModel> get questionConditions =>
      RealmObjectBase.get<QuestionConditionModel>(this, 'questionConditions')
          as RealmList<QuestionConditionModel>;
  @override
  set questionConditions(covariant RealmList<QuestionConditionModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  RealmList<CommentTypeModel> get commentTypes =>
      RealmObjectBase.get<CommentTypeModel>(this, 'commentTypes')
          as RealmList<CommentTypeModel>;
  @override
  set commentTypes(covariant RealmList<CommentTypeModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  DateTime? get modifiedTimeStampQuestion =>
      RealmObjectBase.get<DateTime>(this, 'modifiedTimeStampQuestion')
          as DateTime?;
  @override
  set modifiedTimeStampQuestion(DateTime? value) =>
      RealmObjectBase.set(this, 'modifiedTimeStampQuestion', value);

  @override
  bool? get targetByCycle =>
      RealmObjectBase.get<bool>(this, 'targetByCycle') as bool?;
  @override
  set targetByCycle(bool? value) =>
      RealmObjectBase.set(this, 'targetByCycle', value);

  @override
  bool? get targetByGroup =>
      RealmObjectBase.get<bool>(this, 'targetByGroup') as bool?;
  @override
  set targetByGroup(bool? value) =>
      RealmObjectBase.set(this, 'targetByGroup', value);

  @override
  bool? get targetByCompany =>
      RealmObjectBase.get<bool>(this, 'targetByCompany') as bool?;
  @override
  set targetByCompany(bool? value) =>
      RealmObjectBase.set(this, 'targetByCompany', value);

  @override
  bool? get targetByRegion =>
      RealmObjectBase.get<bool>(this, 'targetByRegion') as bool?;
  @override
  set targetByRegion(bool? value) =>
      RealmObjectBase.set(this, 'targetByRegion', value);

  @override
  bool? get targetByBudget =>
      RealmObjectBase.get<bool>(this, 'targetByBudget') as bool?;
  @override
  set targetByBudget(bool? value) =>
      RealmObjectBase.set(this, 'targetByBudget', value);

  @override
  bool? get isMll => RealmObjectBase.get<bool>(this, 'isMll') as bool?;
  @override
  set isMll(bool? value) => RealmObjectBase.set(this, 'isMll', value);

  @override
  RealmList<PhotoTagsTModel> get photoTagsTwo =>
      RealmObjectBase.get<PhotoTagsTModel>(this, 'photoTagsTwo')
          as RealmList<PhotoTagsTModel>;
  @override
  set photoTagsTwo(covariant RealmList<PhotoTagsTModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  RealmList<PhotoTagsTModel> get photoTagsThree =>
      RealmObjectBase.get<PhotoTagsTModel>(this, 'photoTagsThree')
          as RealmList<PhotoTagsTModel>;
  @override
  set photoTagsThree(covariant RealmList<PhotoTagsTModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  bool? get isMulti => RealmObjectBase.get<bool>(this, 'isMulti') as bool?;
  @override
  set isMulti(bool? value) => RealmObjectBase.set(this, 'isMulti', value);

  @override
  int? get multiMeasurementId =>
      RealmObjectBase.get<int>(this, 'multiMeasurementId') as int?;
  @override
  set multiMeasurementId(int? value) =>
      RealmObjectBase.set(this, 'multiMeasurementId', value);

  @override
  bool? get isMultiOneAnswer =>
      RealmObjectBase.get<bool>(this, 'isMultiOneAnswer') as bool?;
  @override
  set isMultiOneAnswer(bool? value) =>
      RealmObjectBase.set(this, 'isMultiOneAnswer', value);

  @override
  bool? get flip => RealmObjectBase.get<bool>(this, 'flip') as bool?;
  @override
  set flip(bool? value) => RealmObjectBase.set(this, 'flip', value);

  @override
  int? get questionTypeId =>
      RealmObjectBase.get<int>(this, 'questionTypeId') as int?;
  @override
  set questionTypeId(int? value) =>
      RealmObjectBase.set(this, 'questionTypeId', value);

  @override
  Stream<RealmObjectChanges<QuestionModel>> get changes =>
      RealmObjectBase.getChanges<QuestionModel>(this);

  @override
  Stream<RealmObjectChanges<QuestionModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<QuestionModel>(this, keyPaths);

  @override
  QuestionModel freeze() => RealmObjectBase.freezeObject<QuestionModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'questionId': questionId.toEJson(),
      'questionOrderId': questionOrderId.toEJson(),
      'questionDescription': questionDescription.toEJson(),
      'isComment': isComment.toEJson(),
      'isCommentMandatory': isCommentMandatory.toEJson(),
      'showQuestions': showQuestions.toEJson(),
      'hasSignature': hasSignature.toEJson(),
      'isSignatureMandatory': isSignatureMandatory.toEJson(),
      'signatureUrl': signatureUrl.toEJson(),
      'photoUrl': photoUrl.toEJson(),
      'questionBrief': questionBrief.toEJson(),
      'questionParts': questionParts.toEJson(),
      'measurements': measurements.toEJson(),
      'questionConditions': questionConditions.toEJson(),
      'commentTypes': commentTypes.toEJson(),
      'modifiedTimeStampQuestion': modifiedTimeStampQuestion.toEJson(),
      'targetByCycle': targetByCycle.toEJson(),
      'targetByGroup': targetByGroup.toEJson(),
      'targetByCompany': targetByCompany.toEJson(),
      'targetByRegion': targetByRegion.toEJson(),
      'targetByBudget': targetByBudget.toEJson(),
      'isMll': isMll.toEJson(),
      'photoTagsTwo': photoTagsTwo.toEJson(),
      'photoTagsThree': photoTagsThree.toEJson(),
      'isMulti': isMulti.toEJson(),
      'multiMeasurementId': multiMeasurementId.toEJson(),
      'isMultiOneAnswer': isMultiOneAnswer.toEJson(),
      'flip': flip.toEJson(),
      'questionTypeId': questionTypeId.toEJson(),
    };
  }

  static EJsonValue _toEJson(QuestionModel value) => value.toEJson();
  static QuestionModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return QuestionModel(
      questionId: fromEJson(ejson['questionId']),
      questionOrderId: fromEJson(ejson['questionOrderId']),
      questionDescription: fromEJson(ejson['questionDescription']),
      isComment: fromEJson(ejson['isComment']),
      isCommentMandatory: fromEJson(ejson['isCommentMandatory']),
      showQuestions: fromEJson(ejson['showQuestions']),
      hasSignature: fromEJson(ejson['hasSignature']),
      isSignatureMandatory: fromEJson(ejson['isSignatureMandatory']),
      signatureUrl: fromEJson(ejson['signatureUrl']),
      photoUrl: fromEJson(ejson['photoUrl']),
      questionBrief: fromEJson(ejson['questionBrief']),
      questionParts: fromEJson(ejson['questionParts']),
      measurements: fromEJson(ejson['measurements']),
      questionConditions: fromEJson(ejson['questionConditions']),
      commentTypes: fromEJson(ejson['commentTypes']),
      modifiedTimeStampQuestion: fromEJson(ejson['modifiedTimeStampQuestion']),
      targetByCycle: fromEJson(ejson['targetByCycle']),
      targetByGroup: fromEJson(ejson['targetByGroup']),
      targetByCompany: fromEJson(ejson['targetByCompany']),
      targetByRegion: fromEJson(ejson['targetByRegion']),
      targetByBudget: fromEJson(ejson['targetByBudget']),
      isMll: fromEJson(ejson['isMll']),
      photoTagsTwo: fromEJson(ejson['photoTagsTwo']),
      photoTagsThree: fromEJson(ejson['photoTagsThree']),
      isMulti: fromEJson(ejson['isMulti']),
      multiMeasurementId: fromEJson(ejson['multiMeasurementId']),
      isMultiOneAnswer: fromEJson(ejson['isMultiOneAnswer']),
      flip: fromEJson(ejson['flip']),
      questionTypeId: fromEJson(ejson['questionTypeId']),
    );
  }

  static final schema = () {
    RealmObjectBase.registerFactory(QuestionModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, QuestionModel, 'QuestionModel', [
      SchemaProperty('questionId', RealmPropertyType.int, optional: true),
      SchemaProperty('questionOrderId', RealmPropertyType.int, optional: true),
      SchemaProperty('questionDescription', RealmPropertyType.string,
          optional: true),
      SchemaProperty('isComment', RealmPropertyType.bool, optional: true),
      SchemaProperty('isCommentMandatory', RealmPropertyType.bool,
          optional: true),
      SchemaProperty('showQuestions', RealmPropertyType.bool, optional: true),
      SchemaProperty('hasSignature', RealmPropertyType.bool, optional: true),
      SchemaProperty('isSignatureMandatory', RealmPropertyType.bool,
          optional: true),
      SchemaProperty('signatureUrl', RealmPropertyType.string, optional: true),
      SchemaProperty('photoUrl', RealmPropertyType.string, optional: true),
      SchemaProperty('questionBrief', RealmPropertyType.string, optional: true),
      SchemaProperty('questionParts', RealmPropertyType.object,
          linkTarget: 'QuestionPartModel',
          collectionType: RealmCollectionType.list),
      SchemaProperty('measurements', RealmPropertyType.object,
          linkTarget: 'MeasurementModel',
          collectionType: RealmCollectionType.list),
      SchemaProperty('questionConditions', RealmPropertyType.object,
          linkTarget: 'QuestionConditionModel',
          collectionType: RealmCollectionType.list),
      SchemaProperty('commentTypes', RealmPropertyType.object,
          linkTarget: 'CommentTypeModel',
          collectionType: RealmCollectionType.list),
      SchemaProperty('modifiedTimeStampQuestion', RealmPropertyType.timestamp,
          optional: true),
      SchemaProperty('targetByCycle', RealmPropertyType.bool, optional: true),
      SchemaProperty('targetByGroup', RealmPropertyType.bool, optional: true),
      SchemaProperty('targetByCompany', RealmPropertyType.bool, optional: true),
      SchemaProperty('targetByRegion', RealmPropertyType.bool, optional: true),
      SchemaProperty('targetByBudget', RealmPropertyType.bool, optional: true),
      SchemaProperty('isMll', RealmPropertyType.bool, optional: true),
      SchemaProperty('photoTagsTwo', RealmPropertyType.object,
          linkTarget: 'PhotoTagsTModel',
          collectionType: RealmCollectionType.list),
      SchemaProperty('photoTagsThree', RealmPropertyType.object,
          linkTarget: 'PhotoTagsTModel',
          collectionType: RealmCollectionType.list),
      SchemaProperty('isMulti', RealmPropertyType.bool, optional: true),
      SchemaProperty('multiMeasurementId', RealmPropertyType.int,
          optional: true),
      SchemaProperty('isMultiOneAnswer', RealmPropertyType.bool,
          optional: true),
      SchemaProperty('flip', RealmPropertyType.bool, optional: true),
      SchemaProperty('questionTypeId', RealmPropertyType.int, optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class CommentTypeModel extends _CommentTypeModel
    with RealmEntity, RealmObjectBase, RealmObject {
  CommentTypeModel({
    int? commentTypeId,
    String? commentType,
  }) {
    RealmObjectBase.set(this, 'commentTypeId', commentTypeId);
    RealmObjectBase.set(this, 'commentType', commentType);
  }

  CommentTypeModel._();

  @override
  int? get commentTypeId =>
      RealmObjectBase.get<int>(this, 'commentTypeId') as int?;
  @override
  set commentTypeId(int? value) =>
      RealmObjectBase.set(this, 'commentTypeId', value);

  @override
  String? get commentType =>
      RealmObjectBase.get<String>(this, 'commentType') as String?;
  @override
  set commentType(String? value) =>
      RealmObjectBase.set(this, 'commentType', value);

  @override
  Stream<RealmObjectChanges<CommentTypeModel>> get changes =>
      RealmObjectBase.getChanges<CommentTypeModel>(this);

  @override
  Stream<RealmObjectChanges<CommentTypeModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<CommentTypeModel>(this, keyPaths);

  @override
  CommentTypeModel freeze() =>
      RealmObjectBase.freezeObject<CommentTypeModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'commentTypeId': commentTypeId.toEJson(),
      'commentType': commentType.toEJson(),
    };
  }

  static EJsonValue _toEJson(CommentTypeModel value) => value.toEJson();
  static CommentTypeModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return CommentTypeModel(
      commentTypeId: fromEJson(ejson['commentTypeId']),
      commentType: fromEJson(ejson['commentType']),
    );
  }

  static final schema = () {
    RealmObjectBase.registerFactory(CommentTypeModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, CommentTypeModel, 'CommentTypeModel', [
      SchemaProperty('commentTypeId', RealmPropertyType.int, optional: true),
      SchemaProperty('commentType', RealmPropertyType.string, optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class MeasurementModel extends _MeasurementModel
    with RealmEntity, RealmObjectBase, RealmObject {
  MeasurementModel({
    int? measurementId,
    int? measurementTypeId,
    String? measurementDescription,
    String? measurementTypeName,
    String? defaultAction,
    Iterable<MeasurementOptionModel> measurementOptions = const [],
    Iterable<MeasurementConditionModel> measurementConditions = const [],
    Iterable<MeasurementConditionModel> measurementConditionsMultiple =
        const [],
    Iterable<MeasurementValidationModel> measurementValidations = const [],
    String? measurementDefaultsResult,
    DateTime? modifiedTimeStampMeasurement,
    DateTime? modifiedTimeStampMeasurementDefaultsResult,
    int? measurementOrderId,
    int? mandatoryPhototypesCount,
    int? optionalPhototypesCount,
    String? measurementImage,
    int? companyid,
    Iterable<MeasurementPhototypesDeprecatedModel>
        measurementPhototypesDeprecated = const [],
    DateTime? modifiedTimeStampMeasurementvalidation,
    int? validationTypeId,
    bool? required,
    String? rangeValidation,
    String? expressionValidation,
    String? errorMessage,
    DateTime? modifiedTimeStampMeasurementdefault,
  }) {
    RealmObjectBase.set(this, 'measurementId', measurementId);
    RealmObjectBase.set(this, 'measurementTypeId', measurementTypeId);
    RealmObjectBase.set(this, 'measurementDescription', measurementDescription);
    RealmObjectBase.set(this, 'measurementTypeName', measurementTypeName);
    RealmObjectBase.set(this, 'defaultAction', defaultAction);
    RealmObjectBase.set<RealmList<MeasurementOptionModel>>(
        this,
        'measurementOptions',
        RealmList<MeasurementOptionModel>(measurementOptions));
    RealmObjectBase.set<RealmList<MeasurementConditionModel>>(
        this,
        'measurementConditions',
        RealmList<MeasurementConditionModel>(measurementConditions));
    RealmObjectBase.set<RealmList<MeasurementConditionModel>>(
        this,
        'measurementConditionsMultiple',
        RealmList<MeasurementConditionModel>(measurementConditionsMultiple));
    RealmObjectBase.set<RealmList<MeasurementValidationModel>>(
        this,
        'measurementValidations',
        RealmList<MeasurementValidationModel>(measurementValidations));
    RealmObjectBase.set(
        this, 'measurementDefaultsResult', measurementDefaultsResult);
    RealmObjectBase.set(
        this, 'modifiedTimeStampMeasurement', modifiedTimeStampMeasurement);
    RealmObjectBase.set(this, 'modifiedTimeStampMeasurementDefaultsResult',
        modifiedTimeStampMeasurementDefaultsResult);
    RealmObjectBase.set(this, 'measurementOrderId', measurementOrderId);
    RealmObjectBase.set(
        this, 'mandatoryPhototypesCount', mandatoryPhototypesCount);
    RealmObjectBase.set(
        this, 'optionalPhototypesCount', optionalPhototypesCount);
    RealmObjectBase.set(this, 'measurementImage', measurementImage);
    RealmObjectBase.set(this, 'companyid', companyid);
    RealmObjectBase.set<RealmList<MeasurementPhototypesDeprecatedModel>>(
        this,
        'measurementPhototypesDeprecated',
        RealmList<MeasurementPhototypesDeprecatedModel>(
            measurementPhototypesDeprecated));
    RealmObjectBase.set(this, 'modifiedTimeStampMeasurementvalidation',
        modifiedTimeStampMeasurementvalidation);
    RealmObjectBase.set(this, 'validationTypeId', validationTypeId);
    RealmObjectBase.set(this, 'required', required);
    RealmObjectBase.set(this, 'rangeValidation', rangeValidation);
    RealmObjectBase.set(this, 'expressionValidation', expressionValidation);
    RealmObjectBase.set(this, 'errorMessage', errorMessage);
    RealmObjectBase.set(this, 'modifiedTimeStampMeasurementdefault',
        modifiedTimeStampMeasurementdefault);
  }

  MeasurementModel._();

  @override
  int? get measurementId =>
      RealmObjectBase.get<int>(this, 'measurementId') as int?;
  @override
  set measurementId(int? value) =>
      RealmObjectBase.set(this, 'measurementId', value);

  @override
  int? get measurementTypeId =>
      RealmObjectBase.get<int>(this, 'measurementTypeId') as int?;
  @override
  set measurementTypeId(int? value) =>
      RealmObjectBase.set(this, 'measurementTypeId', value);

  @override
  String? get measurementDescription =>
      RealmObjectBase.get<String>(this, 'measurementDescription') as String?;
  @override
  set measurementDescription(String? value) =>
      RealmObjectBase.set(this, 'measurementDescription', value);

  @override
  String? get measurementTypeName =>
      RealmObjectBase.get<String>(this, 'measurementTypeName') as String?;
  @override
  set measurementTypeName(String? value) =>
      RealmObjectBase.set(this, 'measurementTypeName', value);

  @override
  String? get defaultAction =>
      RealmObjectBase.get<String>(this, 'defaultAction') as String?;
  @override
  set defaultAction(String? value) =>
      RealmObjectBase.set(this, 'defaultAction', value);

  @override
  RealmList<MeasurementOptionModel> get measurementOptions =>
      RealmObjectBase.get<MeasurementOptionModel>(this, 'measurementOptions')
          as RealmList<MeasurementOptionModel>;
  @override
  set measurementOptions(covariant RealmList<MeasurementOptionModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  RealmList<MeasurementConditionModel> get measurementConditions =>
      RealmObjectBase.get<MeasurementConditionModel>(
              this, 'measurementConditions')
          as RealmList<MeasurementConditionModel>;
  @override
  set measurementConditions(
          covariant RealmList<MeasurementConditionModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  RealmList<MeasurementConditionModel> get measurementConditionsMultiple =>
      RealmObjectBase.get<MeasurementConditionModel>(
              this, 'measurementConditionsMultiple')
          as RealmList<MeasurementConditionModel>;
  @override
  set measurementConditionsMultiple(
          covariant RealmList<MeasurementConditionModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  RealmList<MeasurementValidationModel> get measurementValidations =>
      RealmObjectBase.get<MeasurementValidationModel>(
              this, 'measurementValidations')
          as RealmList<MeasurementValidationModel>;
  @override
  set measurementValidations(
          covariant RealmList<MeasurementValidationModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  String? get measurementDefaultsResult =>
      RealmObjectBase.get<String>(this, 'measurementDefaultsResult') as String?;
  @override
  set measurementDefaultsResult(String? value) =>
      RealmObjectBase.set(this, 'measurementDefaultsResult', value);

  @override
  DateTime? get modifiedTimeStampMeasurement =>
      RealmObjectBase.get<DateTime>(this, 'modifiedTimeStampMeasurement')
          as DateTime?;
  @override
  set modifiedTimeStampMeasurement(DateTime? value) =>
      RealmObjectBase.set(this, 'modifiedTimeStampMeasurement', value);

  @override
  DateTime? get modifiedTimeStampMeasurementDefaultsResult =>
      RealmObjectBase.get<DateTime>(
          this, 'modifiedTimeStampMeasurementDefaultsResult') as DateTime?;
  @override
  set modifiedTimeStampMeasurementDefaultsResult(DateTime? value) =>
      RealmObjectBase.set(
          this, 'modifiedTimeStampMeasurementDefaultsResult', value);

  @override
  int? get measurementOrderId =>
      RealmObjectBase.get<int>(this, 'measurementOrderId') as int?;
  @override
  set measurementOrderId(int? value) =>
      RealmObjectBase.set(this, 'measurementOrderId', value);

  @override
  int? get mandatoryPhototypesCount =>
      RealmObjectBase.get<int>(this, 'mandatoryPhototypesCount') as int?;
  @override
  set mandatoryPhototypesCount(int? value) =>
      RealmObjectBase.set(this, 'mandatoryPhototypesCount', value);

  @override
  int? get optionalPhototypesCount =>
      RealmObjectBase.get<int>(this, 'optionalPhototypesCount') as int?;
  @override
  set optionalPhototypesCount(int? value) =>
      RealmObjectBase.set(this, 'optionalPhototypesCount', value);

  @override
  String? get measurementImage =>
      RealmObjectBase.get<String>(this, 'measurementImage') as String?;
  @override
  set measurementImage(String? value) =>
      RealmObjectBase.set(this, 'measurementImage', value);

  @override
  int? get companyid => RealmObjectBase.get<int>(this, 'companyid') as int?;
  @override
  set companyid(int? value) => RealmObjectBase.set(this, 'companyid', value);

  @override
  RealmList<MeasurementPhototypesDeprecatedModel>
      get measurementPhototypesDeprecated =>
          RealmObjectBase.get<MeasurementPhototypesDeprecatedModel>(
                  this, 'measurementPhototypesDeprecated')
              as RealmList<MeasurementPhototypesDeprecatedModel>;
  @override
  set measurementPhototypesDeprecated(
          covariant RealmList<MeasurementPhototypesDeprecatedModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  DateTime? get modifiedTimeStampMeasurementvalidation =>
      RealmObjectBase.get<DateTime>(
          this, 'modifiedTimeStampMeasurementvalidation') as DateTime?;
  @override
  set modifiedTimeStampMeasurementvalidation(DateTime? value) =>
      RealmObjectBase.set(
          this, 'modifiedTimeStampMeasurementvalidation', value);

  @override
  int? get validationTypeId =>
      RealmObjectBase.get<int>(this, 'validationTypeId') as int?;
  @override
  set validationTypeId(int? value) =>
      RealmObjectBase.set(this, 'validationTypeId', value);

  @override
  bool? get required => RealmObjectBase.get<bool>(this, 'required') as bool?;
  @override
  set required(bool? value) => RealmObjectBase.set(this, 'required', value);

  @override
  String? get rangeValidation =>
      RealmObjectBase.get<String>(this, 'rangeValidation') as String?;
  @override
  set rangeValidation(String? value) =>
      RealmObjectBase.set(this, 'rangeValidation', value);

  @override
  String? get expressionValidation =>
      RealmObjectBase.get<String>(this, 'expressionValidation') as String?;
  @override
  set expressionValidation(String? value) =>
      RealmObjectBase.set(this, 'expressionValidation', value);

  @override
  String? get errorMessage =>
      RealmObjectBase.get<String>(this, 'errorMessage') as String?;
  @override
  set errorMessage(String? value) =>
      RealmObjectBase.set(this, 'errorMessage', value);

  @override
  DateTime? get modifiedTimeStampMeasurementdefault =>
      RealmObjectBase.get<DateTime>(this, 'modifiedTimeStampMeasurementdefault')
          as DateTime?;
  @override
  set modifiedTimeStampMeasurementdefault(DateTime? value) =>
      RealmObjectBase.set(this, 'modifiedTimeStampMeasurementdefault', value);

  @override
  Stream<RealmObjectChanges<MeasurementModel>> get changes =>
      RealmObjectBase.getChanges<MeasurementModel>(this);

  @override
  Stream<RealmObjectChanges<MeasurementModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<MeasurementModel>(this, keyPaths);

  @override
  MeasurementModel freeze() =>
      RealmObjectBase.freezeObject<MeasurementModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'measurementId': measurementId.toEJson(),
      'measurementTypeId': measurementTypeId.toEJson(),
      'measurementDescription': measurementDescription.toEJson(),
      'measurementTypeName': measurementTypeName.toEJson(),
      'defaultAction': defaultAction.toEJson(),
      'measurementOptions': measurementOptions.toEJson(),
      'measurementConditions': measurementConditions.toEJson(),
      'measurementConditionsMultiple': measurementConditionsMultiple.toEJson(),
      'measurementValidations': measurementValidations.toEJson(),
      'measurementDefaultsResult': measurementDefaultsResult.toEJson(),
      'modifiedTimeStampMeasurement': modifiedTimeStampMeasurement.toEJson(),
      'modifiedTimeStampMeasurementDefaultsResult':
          modifiedTimeStampMeasurementDefaultsResult.toEJson(),
      'measurementOrderId': measurementOrderId.toEJson(),
      'mandatoryPhototypesCount': mandatoryPhototypesCount.toEJson(),
      'optionalPhototypesCount': optionalPhototypesCount.toEJson(),
      'measurementImage': measurementImage.toEJson(),
      'companyid': companyid.toEJson(),
      'measurementPhototypesDeprecated':
          measurementPhototypesDeprecated.toEJson(),
      'modifiedTimeStampMeasurementvalidation':
          modifiedTimeStampMeasurementvalidation.toEJson(),
      'validationTypeId': validationTypeId.toEJson(),
      'required': required.toEJson(),
      'rangeValidation': rangeValidation.toEJson(),
      'expressionValidation': expressionValidation.toEJson(),
      'errorMessage': errorMessage.toEJson(),
      'modifiedTimeStampMeasurementdefault':
          modifiedTimeStampMeasurementdefault.toEJson(),
    };
  }

  static EJsonValue _toEJson(MeasurementModel value) => value.toEJson();
  static MeasurementModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return MeasurementModel(
      measurementId: fromEJson(ejson['measurementId']),
      measurementTypeId: fromEJson(ejson['measurementTypeId']),
      measurementDescription: fromEJson(ejson['measurementDescription']),
      measurementTypeName: fromEJson(ejson['measurementTypeName']),
      defaultAction: fromEJson(ejson['defaultAction']),
      measurementOptions: fromEJson(ejson['measurementOptions']),
      measurementConditions: fromEJson(ejson['measurementConditions']),
      measurementConditionsMultiple:
          fromEJson(ejson['measurementConditionsMultiple']),
      measurementValidations: fromEJson(ejson['measurementValidations']),
      measurementDefaultsResult: fromEJson(ejson['measurementDefaultsResult']),
      modifiedTimeStampMeasurement:
          fromEJson(ejson['modifiedTimeStampMeasurement']),
      modifiedTimeStampMeasurementDefaultsResult:
          fromEJson(ejson['modifiedTimeStampMeasurementDefaultsResult']),
      measurementOrderId: fromEJson(ejson['measurementOrderId']),
      mandatoryPhototypesCount: fromEJson(ejson['mandatoryPhototypesCount']),
      optionalPhototypesCount: fromEJson(ejson['optionalPhototypesCount']),
      measurementImage: fromEJson(ejson['measurementImage']),
      companyid: fromEJson(ejson['companyid']),
      measurementPhototypesDeprecated:
          fromEJson(ejson['measurementPhototypesDeprecated']),
      modifiedTimeStampMeasurementvalidation:
          fromEJson(ejson['modifiedTimeStampMeasurementvalidation']),
      validationTypeId: fromEJson(ejson['validationTypeId']),
      required: fromEJson(ejson['required']),
      rangeValidation: fromEJson(ejson['rangeValidation']),
      expressionValidation: fromEJson(ejson['expressionValidation']),
      errorMessage: fromEJson(ejson['errorMessage']),
      modifiedTimeStampMeasurementdefault:
          fromEJson(ejson['modifiedTimeStampMeasurementdefault']),
    );
  }

  static final schema = () {
    RealmObjectBase.registerFactory(MeasurementModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, MeasurementModel, 'MeasurementModel', [
      SchemaProperty('measurementId', RealmPropertyType.int, optional: true),
      SchemaProperty('measurementTypeId', RealmPropertyType.int,
          optional: true),
      SchemaProperty('measurementDescription', RealmPropertyType.string,
          optional: true),
      SchemaProperty('measurementTypeName', RealmPropertyType.string,
          optional: true),
      SchemaProperty('defaultAction', RealmPropertyType.string, optional: true),
      SchemaProperty('measurementOptions', RealmPropertyType.object,
          linkTarget: 'MeasurementOptionModel',
          collectionType: RealmCollectionType.list),
      SchemaProperty('measurementConditions', RealmPropertyType.object,
          linkTarget: 'MeasurementConditionModel',
          collectionType: RealmCollectionType.list),
      SchemaProperty('measurementConditionsMultiple', RealmPropertyType.object,
          linkTarget: 'MeasurementConditionModel',
          collectionType: RealmCollectionType.list),
      SchemaProperty('measurementValidations', RealmPropertyType.object,
          linkTarget: 'MeasurementValidationModel',
          collectionType: RealmCollectionType.list),
      SchemaProperty('measurementDefaultsResult', RealmPropertyType.string,
          optional: true),
      SchemaProperty(
          'modifiedTimeStampMeasurement', RealmPropertyType.timestamp,
          optional: true),
      SchemaProperty('modifiedTimeStampMeasurementDefaultsResult',
          RealmPropertyType.timestamp,
          optional: true),
      SchemaProperty('measurementOrderId', RealmPropertyType.int,
          optional: true),
      SchemaProperty('mandatoryPhototypesCount', RealmPropertyType.int,
          optional: true),
      SchemaProperty('optionalPhototypesCount', RealmPropertyType.int,
          optional: true),
      SchemaProperty('measurementImage', RealmPropertyType.string,
          optional: true),
      SchemaProperty('companyid', RealmPropertyType.int, optional: true),
      SchemaProperty(
          'measurementPhototypesDeprecated', RealmPropertyType.object,
          linkTarget: 'MeasurementPhototypesDeprecatedModel',
          collectionType: RealmCollectionType.list),
      SchemaProperty(
          'modifiedTimeStampMeasurementvalidation', RealmPropertyType.timestamp,
          optional: true),
      SchemaProperty('validationTypeId', RealmPropertyType.int, optional: true),
      SchemaProperty('required', RealmPropertyType.bool, optional: true),
      SchemaProperty('rangeValidation', RealmPropertyType.string,
          optional: true),
      SchemaProperty('expressionValidation', RealmPropertyType.string,
          optional: true),
      SchemaProperty('errorMessage', RealmPropertyType.string, optional: true),
      SchemaProperty(
          'modifiedTimeStampMeasurementdefault', RealmPropertyType.timestamp,
          optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class MeasurementConditionModel extends _MeasurementConditionModel
    with RealmEntity, RealmObjectBase, RealmObject {
  MeasurementConditionModel({
    int? measurementId,
    int? measurementOptionId,
    int? actionMeasurementId,
    String? action,
    DateTime? modifiedTimeStampMeasurementconidtion,
  }) {
    RealmObjectBase.set(this, 'measurementId', measurementId);
    RealmObjectBase.set(this, 'measurementOptionId', measurementOptionId);
    RealmObjectBase.set(this, 'actionMeasurementId', actionMeasurementId);
    RealmObjectBase.set(this, 'action', action);
    RealmObjectBase.set(this, 'modifiedTimeStampMeasurementconidtion',
        modifiedTimeStampMeasurementconidtion);
  }

  MeasurementConditionModel._();

  @override
  int? get measurementId =>
      RealmObjectBase.get<int>(this, 'measurementId') as int?;
  @override
  set measurementId(int? value) =>
      RealmObjectBase.set(this, 'measurementId', value);

  @override
  int? get measurementOptionId =>
      RealmObjectBase.get<int>(this, 'measurementOptionId') as int?;
  @override
  set measurementOptionId(int? value) =>
      RealmObjectBase.set(this, 'measurementOptionId', value);

  @override
  int? get actionMeasurementId =>
      RealmObjectBase.get<int>(this, 'actionMeasurementId') as int?;
  @override
  set actionMeasurementId(int? value) =>
      RealmObjectBase.set(this, 'actionMeasurementId', value);

  @override
  String? get action => RealmObjectBase.get<String>(this, 'action') as String?;
  @override
  set action(String? value) => RealmObjectBase.set(this, 'action', value);

  @override
  DateTime? get modifiedTimeStampMeasurementconidtion =>
      RealmObjectBase.get<DateTime>(
          this, 'modifiedTimeStampMeasurementconidtion') as DateTime?;
  @override
  set modifiedTimeStampMeasurementconidtion(DateTime? value) =>
      RealmObjectBase.set(this, 'modifiedTimeStampMeasurementconidtion', value);

  @override
  Stream<RealmObjectChanges<MeasurementConditionModel>> get changes =>
      RealmObjectBase.getChanges<MeasurementConditionModel>(this);

  @override
  Stream<RealmObjectChanges<MeasurementConditionModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<MeasurementConditionModel>(this, keyPaths);

  @override
  MeasurementConditionModel freeze() =>
      RealmObjectBase.freezeObject<MeasurementConditionModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'measurementId': measurementId.toEJson(),
      'measurementOptionId': measurementOptionId.toEJson(),
      'actionMeasurementId': actionMeasurementId.toEJson(),
      'action': action.toEJson(),
      'modifiedTimeStampMeasurementconidtion':
          modifiedTimeStampMeasurementconidtion.toEJson(),
    };
  }

  static EJsonValue _toEJson(MeasurementConditionModel value) =>
      value.toEJson();
  static MeasurementConditionModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return MeasurementConditionModel(
      measurementId: fromEJson(ejson['measurementId']),
      measurementOptionId: fromEJson(ejson['measurementOptionId']),
      actionMeasurementId: fromEJson(ejson['actionMeasurementId']),
      action: fromEJson(ejson['action']),
      modifiedTimeStampMeasurementconidtion:
          fromEJson(ejson['modifiedTimeStampMeasurementconidtion']),
    );
  }

  static final schema = () {
    RealmObjectBase.registerFactory(MeasurementConditionModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(ObjectType.realmObject, MeasurementConditionModel,
        'MeasurementConditionModel', [
      SchemaProperty('measurementId', RealmPropertyType.int, optional: true),
      SchemaProperty('measurementOptionId', RealmPropertyType.int,
          optional: true),
      SchemaProperty('actionMeasurementId', RealmPropertyType.int,
          optional: true),
      SchemaProperty('action', RealmPropertyType.string, optional: true),
      SchemaProperty(
          'modifiedTimeStampMeasurementconidtion', RealmPropertyType.timestamp,
          optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class MeasurementOptionModel extends _MeasurementOptionModel
    with RealmEntity, RealmObjectBase, RealmObject {
  MeasurementOptionModel({
    int? measurementId,
    int? measurementOptionId,
    String? measurementOptionDescription,
    DateTime? modifiedTimeStampMeasurementoption,
    int? budgetOffset,
    int? budgetOffsetType,
    bool? isAnswer,
  }) {
    RealmObjectBase.set(this, 'measurementId', measurementId);
    RealmObjectBase.set(this, 'measurementOptionId', measurementOptionId);
    RealmObjectBase.set(
        this, 'measurementOptionDescription', measurementOptionDescription);
    RealmObjectBase.set(this, 'modifiedTimeStampMeasurementoption',
        modifiedTimeStampMeasurementoption);
    RealmObjectBase.set(this, 'budgetOffset', budgetOffset);
    RealmObjectBase.set(this, 'budgetOffsetType', budgetOffsetType);
    RealmObjectBase.set(this, 'isAnswer', isAnswer);
  }

  MeasurementOptionModel._();

  @override
  int? get measurementId =>
      RealmObjectBase.get<int>(this, 'measurementId') as int?;
  @override
  set measurementId(int? value) =>
      RealmObjectBase.set(this, 'measurementId', value);

  @override
  int? get measurementOptionId =>
      RealmObjectBase.get<int>(this, 'measurementOptionId') as int?;
  @override
  set measurementOptionId(int? value) =>
      RealmObjectBase.set(this, 'measurementOptionId', value);

  @override
  String? get measurementOptionDescription =>
      RealmObjectBase.get<String>(this, 'measurementOptionDescription')
          as String?;
  @override
  set measurementOptionDescription(String? value) =>
      RealmObjectBase.set(this, 'measurementOptionDescription', value);

  @override
  DateTime? get modifiedTimeStampMeasurementoption =>
      RealmObjectBase.get<DateTime>(this, 'modifiedTimeStampMeasurementoption')
          as DateTime?;
  @override
  set modifiedTimeStampMeasurementoption(DateTime? value) =>
      RealmObjectBase.set(this, 'modifiedTimeStampMeasurementoption', value);

  @override
  int? get budgetOffset =>
      RealmObjectBase.get<int>(this, 'budgetOffset') as int?;
  @override
  set budgetOffset(int? value) =>
      RealmObjectBase.set(this, 'budgetOffset', value);

  @override
  int? get budgetOffsetType =>
      RealmObjectBase.get<int>(this, 'budgetOffsetType') as int?;
  @override
  set budgetOffsetType(int? value) =>
      RealmObjectBase.set(this, 'budgetOffsetType', value);

  @override
  bool? get isAnswer => RealmObjectBase.get<bool>(this, 'isAnswer') as bool?;
  @override
  set isAnswer(bool? value) => RealmObjectBase.set(this, 'isAnswer', value);

  @override
  Stream<RealmObjectChanges<MeasurementOptionModel>> get changes =>
      RealmObjectBase.getChanges<MeasurementOptionModel>(this);

  @override
  Stream<RealmObjectChanges<MeasurementOptionModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<MeasurementOptionModel>(this, keyPaths);

  @override
  MeasurementOptionModel freeze() =>
      RealmObjectBase.freezeObject<MeasurementOptionModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'measurementId': measurementId.toEJson(),
      'measurementOptionId': measurementOptionId.toEJson(),
      'measurementOptionDescription': measurementOptionDescription.toEJson(),
      'modifiedTimeStampMeasurementoption':
          modifiedTimeStampMeasurementoption.toEJson(),
      'budgetOffset': budgetOffset.toEJson(),
      'budgetOffsetType': budgetOffsetType.toEJson(),
      'isAnswer': isAnswer.toEJson(),
    };
  }

  static EJsonValue _toEJson(MeasurementOptionModel value) => value.toEJson();
  static MeasurementOptionModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return MeasurementOptionModel(
      measurementId: fromEJson(ejson['measurementId']),
      measurementOptionId: fromEJson(ejson['measurementOptionId']),
      measurementOptionDescription:
          fromEJson(ejson['measurementOptionDescription']),
      modifiedTimeStampMeasurementoption:
          fromEJson(ejson['modifiedTimeStampMeasurementoption']),
      budgetOffset: fromEJson(ejson['budgetOffset']),
      budgetOffsetType: fromEJson(ejson['budgetOffsetType']),
      isAnswer: fromEJson(ejson['isAnswer']),
    );
  }

  static final schema = () {
    RealmObjectBase.registerFactory(MeasurementOptionModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(ObjectType.realmObject, MeasurementOptionModel,
        'MeasurementOptionModel', [
      SchemaProperty('measurementId', RealmPropertyType.int, optional: true),
      SchemaProperty('measurementOptionId', RealmPropertyType.int,
          optional: true),
      SchemaProperty('measurementOptionDescription', RealmPropertyType.string,
          optional: true),
      SchemaProperty(
          'modifiedTimeStampMeasurementoption', RealmPropertyType.timestamp,
          optional: true),
      SchemaProperty('budgetOffset', RealmPropertyType.int, optional: true),
      SchemaProperty('budgetOffsetType', RealmPropertyType.int, optional: true),
      SchemaProperty('isAnswer', RealmPropertyType.bool, optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class MeasurementPhototypesDeprecatedModel
    extends _MeasurementPhototypesDeprecatedModel
    with RealmEntity, RealmObjectBase, RealmObject {
  MeasurementPhototypesDeprecatedModel({
    int? uploadedPictureAmount,
    bool? uploadedBlank,
    bool? uploadedAndCompleted,
    Iterable<PhotoModel> photos = const [],
    bool? attribute,
    int? folderId,
    String? folderName,
    int? folderPictureAmount,
    bool? imageRec,
    DateTime? modifiedTimeStampPhototype,
  }) {
    RealmObjectBase.set(this, 'uploadedPictureAmount', uploadedPictureAmount);
    RealmObjectBase.set(this, 'uploadedBlank', uploadedBlank);
    RealmObjectBase.set(this, 'uploadedAndCompleted', uploadedAndCompleted);
    RealmObjectBase.set<RealmList<PhotoModel>>(
        this, 'photos', RealmList<PhotoModel>(photos));
    RealmObjectBase.set(this, 'attribute', attribute);
    RealmObjectBase.set(this, 'folderId', folderId);
    RealmObjectBase.set(this, 'folderName', folderName);
    RealmObjectBase.set(this, 'folderPictureAmount', folderPictureAmount);
    RealmObjectBase.set(this, 'imageRec', imageRec);
    RealmObjectBase.set(
        this, 'modifiedTimeStampPhototype', modifiedTimeStampPhototype);
  }

  MeasurementPhototypesDeprecatedModel._();

  @override
  int? get uploadedPictureAmount =>
      RealmObjectBase.get<int>(this, 'uploadedPictureAmount') as int?;
  @override
  set uploadedPictureAmount(int? value) =>
      RealmObjectBase.set(this, 'uploadedPictureAmount', value);

  @override
  bool? get uploadedBlank =>
      RealmObjectBase.get<bool>(this, 'uploadedBlank') as bool?;
  @override
  set uploadedBlank(bool? value) =>
      RealmObjectBase.set(this, 'uploadedBlank', value);

  @override
  bool? get uploadedAndCompleted =>
      RealmObjectBase.get<bool>(this, 'uploadedAndCompleted') as bool?;
  @override
  set uploadedAndCompleted(bool? value) =>
      RealmObjectBase.set(this, 'uploadedAndCompleted', value);

  @override
  RealmList<PhotoModel> get photos =>
      RealmObjectBase.get<PhotoModel>(this, 'photos') as RealmList<PhotoModel>;
  @override
  set photos(covariant RealmList<PhotoModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  bool? get attribute => RealmObjectBase.get<bool>(this, 'attribute') as bool?;
  @override
  set attribute(bool? value) => RealmObjectBase.set(this, 'attribute', value);

  @override
  int? get folderId => RealmObjectBase.get<int>(this, 'folderId') as int?;
  @override
  set folderId(int? value) => RealmObjectBase.set(this, 'folderId', value);

  @override
  String? get folderName =>
      RealmObjectBase.get<String>(this, 'folderName') as String?;
  @override
  set folderName(String? value) =>
      RealmObjectBase.set(this, 'folderName', value);

  @override
  int? get folderPictureAmount =>
      RealmObjectBase.get<int>(this, 'folderPictureAmount') as int?;
  @override
  set folderPictureAmount(int? value) =>
      RealmObjectBase.set(this, 'folderPictureAmount', value);

  @override
  bool? get imageRec => RealmObjectBase.get<bool>(this, 'imageRec') as bool?;
  @override
  set imageRec(bool? value) => RealmObjectBase.set(this, 'imageRec', value);

  @override
  DateTime? get modifiedTimeStampPhototype =>
      RealmObjectBase.get<DateTime>(this, 'modifiedTimeStampPhototype')
          as DateTime?;
  @override
  set modifiedTimeStampPhototype(DateTime? value) =>
      RealmObjectBase.set(this, 'modifiedTimeStampPhototype', value);

  @override
  Stream<RealmObjectChanges<MeasurementPhototypesDeprecatedModel>>
      get changes =>
          RealmObjectBase.getChanges<MeasurementPhototypesDeprecatedModel>(
              this);

  @override
  Stream<RealmObjectChanges<MeasurementPhototypesDeprecatedModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<MeasurementPhototypesDeprecatedModel>(
          this, keyPaths);

  @override
  MeasurementPhototypesDeprecatedModel freeze() =>
      RealmObjectBase.freezeObject<MeasurementPhototypesDeprecatedModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'uploadedPictureAmount': uploadedPictureAmount.toEJson(),
      'uploadedBlank': uploadedBlank.toEJson(),
      'uploadedAndCompleted': uploadedAndCompleted.toEJson(),
      'photos': photos.toEJson(),
      'attribute': attribute.toEJson(),
      'folderId': folderId.toEJson(),
      'folderName': folderName.toEJson(),
      'folderPictureAmount': folderPictureAmount.toEJson(),
      'imageRec': imageRec.toEJson(),
      'modifiedTimeStampPhototype': modifiedTimeStampPhototype.toEJson(),
    };
  }

  static EJsonValue _toEJson(MeasurementPhototypesDeprecatedModel value) =>
      value.toEJson();
  static MeasurementPhototypesDeprecatedModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return MeasurementPhototypesDeprecatedModel(
      uploadedPictureAmount: fromEJson(ejson['uploadedPictureAmount']),
      uploadedBlank: fromEJson(ejson['uploadedBlank']),
      uploadedAndCompleted: fromEJson(ejson['uploadedAndCompleted']),
      photos: fromEJson(ejson['photos']),
      attribute: fromEJson(ejson['attribute']),
      folderId: fromEJson(ejson['folderId']),
      folderName: fromEJson(ejson['folderName']),
      folderPictureAmount: fromEJson(ejson['folderPictureAmount']),
      imageRec: fromEJson(ejson['imageRec']),
      modifiedTimeStampPhototype:
          fromEJson(ejson['modifiedTimeStampPhototype']),
    );
  }

  static final schema = () {
    RealmObjectBase.registerFactory(MeasurementPhototypesDeprecatedModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject,
        MeasurementPhototypesDeprecatedModel,
        'MeasurementPhototypesDeprecatedModel', [
      SchemaProperty('uploadedPictureAmount', RealmPropertyType.int,
          optional: true),
      SchemaProperty('uploadedBlank', RealmPropertyType.bool, optional: true),
      SchemaProperty('uploadedAndCompleted', RealmPropertyType.bool,
          optional: true),
      SchemaProperty('photos', RealmPropertyType.object,
          linkTarget: 'PhotoModel', collectionType: RealmCollectionType.list),
      SchemaProperty('attribute', RealmPropertyType.bool, optional: true),
      SchemaProperty('folderId', RealmPropertyType.int, optional: true),
      SchemaProperty('folderName', RealmPropertyType.string, optional: true),
      SchemaProperty('folderPictureAmount', RealmPropertyType.int,
          optional: true),
      SchemaProperty('imageRec', RealmPropertyType.bool, optional: true),
      SchemaProperty('modifiedTimeStampPhototype', RealmPropertyType.timestamp,
          optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class PhotoModel extends _PhotoModel
    with RealmEntity, RealmObjectBase, RealmObject {
  PhotoModel({
    int? formId,
    int? questionId,
    int? measurementId,
    int? folderId,
    int? photoId,
    String? photoUrl,
    String? thumbnailUrl,
    String? caption,
    DateTime? modifiedTimeStampPhoto,
    bool? cannotUploadMandatory,
    bool? userDeletedPhoto,
    bool? imageRec,
    int? questionpartId,
    String? questionPartMultiId,
    int? measurementPhototypeId,
    int? combineTypeId,
    int? photoTagId,
    int? photoCombinetypeId,
  }) {
    RealmObjectBase.set(this, 'formId', formId);
    RealmObjectBase.set(this, 'questionId', questionId);
    RealmObjectBase.set(this, 'measurementId', measurementId);
    RealmObjectBase.set(this, 'folderId', folderId);
    RealmObjectBase.set(this, 'photoId', photoId);
    RealmObjectBase.set(this, 'photoUrl', photoUrl);
    RealmObjectBase.set(this, 'thumbnailUrl', thumbnailUrl);
    RealmObjectBase.set(this, 'caption', caption);
    RealmObjectBase.set(this, 'modifiedTimeStampPhoto', modifiedTimeStampPhoto);
    RealmObjectBase.set(this, 'cannotUploadMandatory', cannotUploadMandatory);
    RealmObjectBase.set(this, 'userDeletedPhoto', userDeletedPhoto);
    RealmObjectBase.set(this, 'imageRec', imageRec);
    RealmObjectBase.set(this, 'questionpartId', questionpartId);
    RealmObjectBase.set(this, 'questionPartMultiId', questionPartMultiId);
    RealmObjectBase.set(this, 'measurementPhototypeId', measurementPhototypeId);
    RealmObjectBase.set(this, 'combineTypeId', combineTypeId);
    RealmObjectBase.set(this, 'photoTagId', photoTagId);
    RealmObjectBase.set(this, 'photoCombinetypeId', photoCombinetypeId);
  }

  PhotoModel._();

  @override
  int? get formId => RealmObjectBase.get<int>(this, 'formId') as int?;
  @override
  set formId(int? value) => RealmObjectBase.set(this, 'formId', value);

  @override
  int? get questionId => RealmObjectBase.get<int>(this, 'questionId') as int?;
  @override
  set questionId(int? value) => RealmObjectBase.set(this, 'questionId', value);

  @override
  int? get measurementId =>
      RealmObjectBase.get<int>(this, 'measurementId') as int?;
  @override
  set measurementId(int? value) =>
      RealmObjectBase.set(this, 'measurementId', value);

  @override
  int? get folderId => RealmObjectBase.get<int>(this, 'folderId') as int?;
  @override
  set folderId(int? value) => RealmObjectBase.set(this, 'folderId', value);

  @override
  int? get photoId => RealmObjectBase.get<int>(this, 'photoId') as int?;
  @override
  set photoId(int? value) => RealmObjectBase.set(this, 'photoId', value);

  @override
  String? get photoUrl =>
      RealmObjectBase.get<String>(this, 'photoUrl') as String?;
  @override
  set photoUrl(String? value) => RealmObjectBase.set(this, 'photoUrl', value);

  @override
  String? get thumbnailUrl =>
      RealmObjectBase.get<String>(this, 'thumbnailUrl') as String?;
  @override
  set thumbnailUrl(String? value) =>
      RealmObjectBase.set(this, 'thumbnailUrl', value);

  @override
  String? get caption =>
      RealmObjectBase.get<String>(this, 'caption') as String?;
  @override
  set caption(String? value) => RealmObjectBase.set(this, 'caption', value);

  @override
  DateTime? get modifiedTimeStampPhoto =>
      RealmObjectBase.get<DateTime>(this, 'modifiedTimeStampPhoto')
          as DateTime?;
  @override
  set modifiedTimeStampPhoto(DateTime? value) =>
      RealmObjectBase.set(this, 'modifiedTimeStampPhoto', value);

  @override
  bool? get cannotUploadMandatory =>
      RealmObjectBase.get<bool>(this, 'cannotUploadMandatory') as bool?;
  @override
  set cannotUploadMandatory(bool? value) =>
      RealmObjectBase.set(this, 'cannotUploadMandatory', value);

  @override
  bool? get userDeletedPhoto =>
      RealmObjectBase.get<bool>(this, 'userDeletedPhoto') as bool?;
  @override
  set userDeletedPhoto(bool? value) =>
      RealmObjectBase.set(this, 'userDeletedPhoto', value);

  @override
  bool? get imageRec => RealmObjectBase.get<bool>(this, 'imageRec') as bool?;
  @override
  set imageRec(bool? value) => RealmObjectBase.set(this, 'imageRec', value);

  @override
  int? get questionpartId =>
      RealmObjectBase.get<int>(this, 'questionpartId') as int?;
  @override
  set questionpartId(int? value) =>
      RealmObjectBase.set(this, 'questionpartId', value);

  @override
  String? get questionPartMultiId =>
      RealmObjectBase.get<String>(this, 'questionPartMultiId') as String?;
  @override
  set questionPartMultiId(String? value) =>
      RealmObjectBase.set(this, 'questionPartMultiId', value);

  @override
  int? get measurementPhototypeId =>
      RealmObjectBase.get<int>(this, 'measurementPhototypeId') as int?;
  @override
  set measurementPhototypeId(int? value) =>
      RealmObjectBase.set(this, 'measurementPhototypeId', value);

  @override
  int? get combineTypeId =>
      RealmObjectBase.get<int>(this, 'combineTypeId') as int?;
  @override
  set combineTypeId(int? value) =>
      RealmObjectBase.set(this, 'combineTypeId', value);

  @override
  int? get photoTagId => RealmObjectBase.get<int>(this, 'photoTagId') as int?;
  @override
  set photoTagId(int? value) => RealmObjectBase.set(this, 'photoTagId', value);

  @override
  int? get photoCombinetypeId =>
      RealmObjectBase.get<int>(this, 'photoCombinetypeId') as int?;
  @override
  set photoCombinetypeId(int? value) =>
      RealmObjectBase.set(this, 'photoCombinetypeId', value);

  @override
  Stream<RealmObjectChanges<PhotoModel>> get changes =>
      RealmObjectBase.getChanges<PhotoModel>(this);

  @override
  Stream<RealmObjectChanges<PhotoModel>> changesFor([List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<PhotoModel>(this, keyPaths);

  @override
  PhotoModel freeze() => RealmObjectBase.freezeObject<PhotoModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'formId': formId.toEJson(),
      'questionId': questionId.toEJson(),
      'measurementId': measurementId.toEJson(),
      'folderId': folderId.toEJson(),
      'photoId': photoId.toEJson(),
      'photoUrl': photoUrl.toEJson(),
      'thumbnailUrl': thumbnailUrl.toEJson(),
      'caption': caption.toEJson(),
      'modifiedTimeStampPhoto': modifiedTimeStampPhoto.toEJson(),
      'cannotUploadMandatory': cannotUploadMandatory.toEJson(),
      'userDeletedPhoto': userDeletedPhoto.toEJson(),
      'imageRec': imageRec.toEJson(),
      'questionpartId': questionpartId.toEJson(),
      'questionPartMultiId': questionPartMultiId.toEJson(),
      'measurementPhototypeId': measurementPhototypeId.toEJson(),
      'combineTypeId': combineTypeId.toEJson(),
      'photoTagId': photoTagId.toEJson(),
      'photoCombinetypeId': photoCombinetypeId.toEJson(),
    };
  }

  static EJsonValue _toEJson(PhotoModel value) => value.toEJson();
  static PhotoModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return PhotoModel(
      formId: fromEJson(ejson['formId']),
      questionId: fromEJson(ejson['questionId']),
      measurementId: fromEJson(ejson['measurementId']),
      folderId: fromEJson(ejson['folderId']),
      photoId: fromEJson(ejson['photoId']),
      photoUrl: fromEJson(ejson['photoUrl']),
      thumbnailUrl: fromEJson(ejson['thumbnailUrl']),
      caption: fromEJson(ejson['caption']),
      modifiedTimeStampPhoto: fromEJson(ejson['modifiedTimeStampPhoto']),
      cannotUploadMandatory: fromEJson(ejson['cannotUploadMandatory']),
      userDeletedPhoto: fromEJson(ejson['userDeletedPhoto']),
      imageRec: fromEJson(ejson['imageRec']),
      questionpartId: fromEJson(ejson['questionpartId']),
      questionPartMultiId: fromEJson(ejson['questionPartMultiId']),
      measurementPhototypeId: fromEJson(ejson['measurementPhototypeId']),
      combineTypeId: fromEJson(ejson['combineTypeId']),
      photoTagId: fromEJson(ejson['photoTagId']),
      photoCombinetypeId: fromEJson(ejson['photoCombinetypeId']),
    );
  }

  static final schema = () {
    RealmObjectBase.registerFactory(PhotoModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, PhotoModel, 'PhotoModel', [
      SchemaProperty('formId', RealmPropertyType.int, optional: true),
      SchemaProperty('questionId', RealmPropertyType.int, optional: true),
      SchemaProperty('measurementId', RealmPropertyType.int, optional: true),
      SchemaProperty('folderId', RealmPropertyType.int, optional: true),
      SchemaProperty('photoId', RealmPropertyType.int, optional: true),
      SchemaProperty('photoUrl', RealmPropertyType.string, optional: true),
      SchemaProperty('thumbnailUrl', RealmPropertyType.string, optional: true),
      SchemaProperty('caption', RealmPropertyType.string, optional: true),
      SchemaProperty('modifiedTimeStampPhoto', RealmPropertyType.timestamp,
          optional: true),
      SchemaProperty('cannotUploadMandatory', RealmPropertyType.bool,
          optional: true),
      SchemaProperty('userDeletedPhoto', RealmPropertyType.bool,
          optional: true),
      SchemaProperty('imageRec', RealmPropertyType.bool, optional: true),
      SchemaProperty('questionpartId', RealmPropertyType.int, optional: true),
      SchemaProperty('questionPartMultiId', RealmPropertyType.string,
          optional: true),
      SchemaProperty('measurementPhototypeId', RealmPropertyType.int,
          optional: true),
      SchemaProperty('combineTypeId', RealmPropertyType.int, optional: true),
      SchemaProperty('photoTagId', RealmPropertyType.int, optional: true),
      SchemaProperty('photoCombinetypeId', RealmPropertyType.int,
          optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class MeasurementValidationModel extends _MeasurementValidationModel
    with RealmEntity, RealmObjectBase, RealmObject {
  MeasurementValidationModel({
    int? measurementId,
    int? validationTypeId,
    bool? required,
    String? rangeValidation,
    String? expressionValidation,
    String? errorMessage,
    DateTime? modifiedTimeStampMeasurementvalidation,
  }) {
    RealmObjectBase.set(this, 'measurementId', measurementId);
    RealmObjectBase.set(this, 'validationTypeId', validationTypeId);
    RealmObjectBase.set(this, 'required', required);
    RealmObjectBase.set(this, 'rangeValidation', rangeValidation);
    RealmObjectBase.set(this, 'expressionValidation', expressionValidation);
    RealmObjectBase.set(this, 'errorMessage', errorMessage);
    RealmObjectBase.set(this, 'modifiedTimeStampMeasurementvalidation',
        modifiedTimeStampMeasurementvalidation);
  }

  MeasurementValidationModel._();

  @override
  int? get measurementId =>
      RealmObjectBase.get<int>(this, 'measurementId') as int?;
  @override
  set measurementId(int? value) =>
      RealmObjectBase.set(this, 'measurementId', value);

  @override
  int? get validationTypeId =>
      RealmObjectBase.get<int>(this, 'validationTypeId') as int?;
  @override
  set validationTypeId(int? value) =>
      RealmObjectBase.set(this, 'validationTypeId', value);

  @override
  bool? get required => RealmObjectBase.get<bool>(this, 'required') as bool?;
  @override
  set required(bool? value) => RealmObjectBase.set(this, 'required', value);

  @override
  String? get rangeValidation =>
      RealmObjectBase.get<String>(this, 'rangeValidation') as String?;
  @override
  set rangeValidation(String? value) =>
      RealmObjectBase.set(this, 'rangeValidation', value);

  @override
  String? get expressionValidation =>
      RealmObjectBase.get<String>(this, 'expressionValidation') as String?;
  @override
  set expressionValidation(String? value) =>
      RealmObjectBase.set(this, 'expressionValidation', value);

  @override
  String? get errorMessage =>
      RealmObjectBase.get<String>(this, 'errorMessage') as String?;
  @override
  set errorMessage(String? value) =>
      RealmObjectBase.set(this, 'errorMessage', value);

  @override
  DateTime? get modifiedTimeStampMeasurementvalidation =>
      RealmObjectBase.get<DateTime>(
          this, 'modifiedTimeStampMeasurementvalidation') as DateTime?;
  @override
  set modifiedTimeStampMeasurementvalidation(DateTime? value) =>
      RealmObjectBase.set(
          this, 'modifiedTimeStampMeasurementvalidation', value);

  @override
  Stream<RealmObjectChanges<MeasurementValidationModel>> get changes =>
      RealmObjectBase.getChanges<MeasurementValidationModel>(this);

  @override
  Stream<RealmObjectChanges<MeasurementValidationModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<MeasurementValidationModel>(this, keyPaths);

  @override
  MeasurementValidationModel freeze() =>
      RealmObjectBase.freezeObject<MeasurementValidationModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'measurementId': measurementId.toEJson(),
      'validationTypeId': validationTypeId.toEJson(),
      'required': required.toEJson(),
      'rangeValidation': rangeValidation.toEJson(),
      'expressionValidation': expressionValidation.toEJson(),
      'errorMessage': errorMessage.toEJson(),
      'modifiedTimeStampMeasurementvalidation':
          modifiedTimeStampMeasurementvalidation.toEJson(),
    };
  }

  static EJsonValue _toEJson(MeasurementValidationModel value) =>
      value.toEJson();
  static MeasurementValidationModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return MeasurementValidationModel(
      measurementId: fromEJson(ejson['measurementId']),
      validationTypeId: fromEJson(ejson['validationTypeId']),
      required: fromEJson(ejson['required']),
      rangeValidation: fromEJson(ejson['rangeValidation']),
      expressionValidation: fromEJson(ejson['expressionValidation']),
      errorMessage: fromEJson(ejson['errorMessage']),
      modifiedTimeStampMeasurementvalidation:
          fromEJson(ejson['modifiedTimeStampMeasurementvalidation']),
    );
  }

  static final schema = () {
    RealmObjectBase.registerFactory(MeasurementValidationModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(ObjectType.realmObject,
        MeasurementValidationModel, 'MeasurementValidationModel', [
      SchemaProperty('measurementId', RealmPropertyType.int, optional: true),
      SchemaProperty('validationTypeId', RealmPropertyType.int, optional: true),
      SchemaProperty('required', RealmPropertyType.bool, optional: true),
      SchemaProperty('rangeValidation', RealmPropertyType.string,
          optional: true),
      SchemaProperty('expressionValidation', RealmPropertyType.string,
          optional: true),
      SchemaProperty('errorMessage', RealmPropertyType.string, optional: true),
      SchemaProperty(
          'modifiedTimeStampMeasurementvalidation', RealmPropertyType.timestamp,
          optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class PhotoTagsTModel extends _PhotoTagsTModel
    with RealmEntity, RealmObjectBase, RealmObject {
  PhotoTagsTModel({
    int? questionpartId,
    int? measurementId,
    bool? isMandatory,
    int? photoResPerc,
    bool? liveImagesOnly,
    int? photoTagId,
    String? photoTag,
    int? numberOfPhotos,
    int? measurementPhototypeId,
    bool? imageRec,
    Iterable<PhotoModel> userPhotos = const [],
  }) {
    RealmObjectBase.set(this, 'questionpartId', questionpartId);
    RealmObjectBase.set(this, 'measurementId', measurementId);
    RealmObjectBase.set(this, 'isMandatory', isMandatory);
    RealmObjectBase.set(this, 'photoResPerc', photoResPerc);
    RealmObjectBase.set(this, 'liveImagesOnly', liveImagesOnly);
    RealmObjectBase.set(this, 'photoTagId', photoTagId);
    RealmObjectBase.set(this, 'photoTag', photoTag);
    RealmObjectBase.set(this, 'numberOfPhotos', numberOfPhotos);
    RealmObjectBase.set(this, 'measurementPhototypeId', measurementPhototypeId);
    RealmObjectBase.set(this, 'imageRec', imageRec);
    RealmObjectBase.set<RealmList<PhotoModel>>(
        this, 'userPhotos', RealmList<PhotoModel>(userPhotos));
  }

  PhotoTagsTModel._();

  @override
  int? get questionpartId =>
      RealmObjectBase.get<int>(this, 'questionpartId') as int?;
  @override
  set questionpartId(int? value) =>
      RealmObjectBase.set(this, 'questionpartId', value);

  @override
  int? get measurementId =>
      RealmObjectBase.get<int>(this, 'measurementId') as int?;
  @override
  set measurementId(int? value) =>
      RealmObjectBase.set(this, 'measurementId', value);

  @override
  bool? get isMandatory =>
      RealmObjectBase.get<bool>(this, 'isMandatory') as bool?;
  @override
  set isMandatory(bool? value) =>
      RealmObjectBase.set(this, 'isMandatory', value);

  @override
  int? get photoResPerc =>
      RealmObjectBase.get<int>(this, 'photoResPerc') as int?;
  @override
  set photoResPerc(int? value) =>
      RealmObjectBase.set(this, 'photoResPerc', value);

  @override
  bool? get liveImagesOnly =>
      RealmObjectBase.get<bool>(this, 'liveImagesOnly') as bool?;
  @override
  set liveImagesOnly(bool? value) =>
      RealmObjectBase.set(this, 'liveImagesOnly', value);

  @override
  int? get photoTagId => RealmObjectBase.get<int>(this, 'photoTagId') as int?;
  @override
  set photoTagId(int? value) => RealmObjectBase.set(this, 'photoTagId', value);

  @override
  String? get photoTag =>
      RealmObjectBase.get<String>(this, 'photoTag') as String?;
  @override
  set photoTag(String? value) => RealmObjectBase.set(this, 'photoTag', value);

  @override
  int? get numberOfPhotos =>
      RealmObjectBase.get<int>(this, 'numberOfPhotos') as int?;
  @override
  set numberOfPhotos(int? value) =>
      RealmObjectBase.set(this, 'numberOfPhotos', value);

  @override
  int? get measurementPhototypeId =>
      RealmObjectBase.get<int>(this, 'measurementPhototypeId') as int?;
  @override
  set measurementPhototypeId(int? value) =>
      RealmObjectBase.set(this, 'measurementPhototypeId', value);

  @override
  bool? get imageRec => RealmObjectBase.get<bool>(this, 'imageRec') as bool?;
  @override
  set imageRec(bool? value) => RealmObjectBase.set(this, 'imageRec', value);

  @override
  RealmList<PhotoModel> get userPhotos =>
      RealmObjectBase.get<PhotoModel>(this, 'userPhotos')
          as RealmList<PhotoModel>;
  @override
  set userPhotos(covariant RealmList<PhotoModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  Stream<RealmObjectChanges<PhotoTagsTModel>> get changes =>
      RealmObjectBase.getChanges<PhotoTagsTModel>(this);

  @override
  Stream<RealmObjectChanges<PhotoTagsTModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<PhotoTagsTModel>(this, keyPaths);

  @override
  PhotoTagsTModel freeze() =>
      RealmObjectBase.freezeObject<PhotoTagsTModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'questionpartId': questionpartId.toEJson(),
      'measurementId': measurementId.toEJson(),
      'isMandatory': isMandatory.toEJson(),
      'photoResPerc': photoResPerc.toEJson(),
      'liveImagesOnly': liveImagesOnly.toEJson(),
      'photoTagId': photoTagId.toEJson(),
      'photoTag': photoTag.toEJson(),
      'numberOfPhotos': numberOfPhotos.toEJson(),
      'measurementPhototypeId': measurementPhototypeId.toEJson(),
      'imageRec': imageRec.toEJson(),
      'userPhotos': userPhotos.toEJson(),
    };
  }

  static EJsonValue _toEJson(PhotoTagsTModel value) => value.toEJson();
  static PhotoTagsTModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return PhotoTagsTModel(
      questionpartId: fromEJson(ejson['questionpartId']),
      measurementId: fromEJson(ejson['measurementId']),
      isMandatory: fromEJson(ejson['isMandatory']),
      photoResPerc: fromEJson(ejson['photoResPerc']),
      liveImagesOnly: fromEJson(ejson['liveImagesOnly']),
      photoTagId: fromEJson(ejson['photoTagId']),
      photoTag: fromEJson(ejson['photoTag']),
      numberOfPhotos: fromEJson(ejson['numberOfPhotos']),
      measurementPhototypeId: fromEJson(ejson['measurementPhototypeId']),
      imageRec: fromEJson(ejson['imageRec']),
      userPhotos: fromEJson(ejson['userPhotos']),
    );
  }

  static final schema = () {
    RealmObjectBase.registerFactory(PhotoTagsTModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, PhotoTagsTModel, 'PhotoTagsTModel', [
      SchemaProperty('questionpartId', RealmPropertyType.int, optional: true),
      SchemaProperty('measurementId', RealmPropertyType.int, optional: true),
      SchemaProperty('isMandatory', RealmPropertyType.bool, optional: true),
      SchemaProperty('photoResPerc', RealmPropertyType.int, optional: true),
      SchemaProperty('liveImagesOnly', RealmPropertyType.bool, optional: true),
      SchemaProperty('photoTagId', RealmPropertyType.int, optional: true),
      SchemaProperty('photoTag', RealmPropertyType.string, optional: true),
      SchemaProperty('numberOfPhotos', RealmPropertyType.int, optional: true),
      SchemaProperty('measurementPhototypeId', RealmPropertyType.int,
          optional: true),
      SchemaProperty('imageRec', RealmPropertyType.bool, optional: true),
      SchemaProperty('userPhotos', RealmPropertyType.object,
          linkTarget: 'PhotoModel', collectionType: RealmCollectionType.list),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class QuestionConditionModel extends _QuestionConditionModel
    with RealmEntity, RealmObjectBase, RealmObject {
  QuestionConditionModel({
    int? measurementId,
    int? measurementOptionId,
    int? actionQuestionId,
    String? action,
    DateTime? modifiedTimeStampQuestioncondition,
  }) {
    RealmObjectBase.set(this, 'measurementId', measurementId);
    RealmObjectBase.set(this, 'measurementOptionId', measurementOptionId);
    RealmObjectBase.set(this, 'actionQuestionId', actionQuestionId);
    RealmObjectBase.set(this, 'action', action);
    RealmObjectBase.set(this, 'modifiedTimeStampQuestioncondition',
        modifiedTimeStampQuestioncondition);
  }

  QuestionConditionModel._();

  @override
  int? get measurementId =>
      RealmObjectBase.get<int>(this, 'measurementId') as int?;
  @override
  set measurementId(int? value) =>
      RealmObjectBase.set(this, 'measurementId', value);

  @override
  int? get measurementOptionId =>
      RealmObjectBase.get<int>(this, 'measurementOptionId') as int?;
  @override
  set measurementOptionId(int? value) =>
      RealmObjectBase.set(this, 'measurementOptionId', value);

  @override
  int? get actionQuestionId =>
      RealmObjectBase.get<int>(this, 'actionQuestionId') as int?;
  @override
  set actionQuestionId(int? value) =>
      RealmObjectBase.set(this, 'actionQuestionId', value);

  @override
  String? get action => RealmObjectBase.get<String>(this, 'action') as String?;
  @override
  set action(String? value) => RealmObjectBase.set(this, 'action', value);

  @override
  DateTime? get modifiedTimeStampQuestioncondition =>
      RealmObjectBase.get<DateTime>(this, 'modifiedTimeStampQuestioncondition')
          as DateTime?;
  @override
  set modifiedTimeStampQuestioncondition(DateTime? value) =>
      RealmObjectBase.set(this, 'modifiedTimeStampQuestioncondition', value);

  @override
  Stream<RealmObjectChanges<QuestionConditionModel>> get changes =>
      RealmObjectBase.getChanges<QuestionConditionModel>(this);

  @override
  Stream<RealmObjectChanges<QuestionConditionModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<QuestionConditionModel>(this, keyPaths);

  @override
  QuestionConditionModel freeze() =>
      RealmObjectBase.freezeObject<QuestionConditionModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'measurementId': measurementId.toEJson(),
      'measurementOptionId': measurementOptionId.toEJson(),
      'actionQuestionId': actionQuestionId.toEJson(),
      'action': action.toEJson(),
      'modifiedTimeStampQuestioncondition':
          modifiedTimeStampQuestioncondition.toEJson(),
    };
  }

  static EJsonValue _toEJson(QuestionConditionModel value) => value.toEJson();
  static QuestionConditionModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return QuestionConditionModel(
      measurementId: fromEJson(ejson['measurementId']),
      measurementOptionId: fromEJson(ejson['measurementOptionId']),
      actionQuestionId: fromEJson(ejson['actionQuestionId']),
      action: fromEJson(ejson['action']),
      modifiedTimeStampQuestioncondition:
          fromEJson(ejson['modifiedTimeStampQuestioncondition']),
    );
  }

  static final schema = () {
    RealmObjectBase.registerFactory(QuestionConditionModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(ObjectType.realmObject, QuestionConditionModel,
        'QuestionConditionModel', [
      SchemaProperty('measurementId', RealmPropertyType.int, optional: true),
      SchemaProperty('measurementOptionId', RealmPropertyType.int,
          optional: true),
      SchemaProperty('actionQuestionId', RealmPropertyType.int, optional: true),
      SchemaProperty('action', RealmPropertyType.string, optional: true),
      SchemaProperty(
          'modifiedTimeStampQuestioncondition', RealmPropertyType.timestamp,
          optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class QuestionPartModel extends _QuestionPartModel
    with RealmEntity, RealmObjectBase, RealmObject {
  QuestionPartModel({
    int? projectid,
    int? questionpartId,
    String? questionpartDescription,
    String? price,
    DateTime? modifiedTimeStampQuestionpart,
    bool? targetByCycle,
    bool? targetByGroup,
    bool? targetByCompany,
    bool? targetByRegion,
    bool? targetByBudget,
    bool? osaForm,
    int? companyId,
    String? itemImage,
    int? targeted,
  }) {
    RealmObjectBase.set(this, 'projectid', projectid);
    RealmObjectBase.set(this, 'questionpartId', questionpartId);
    RealmObjectBase.set(
        this, 'questionpartDescription', questionpartDescription);
    RealmObjectBase.set(this, 'price', price);
    RealmObjectBase.set(
        this, 'modifiedTimeStampQuestionpart', modifiedTimeStampQuestionpart);
    RealmObjectBase.set(this, 'targetByCycle', targetByCycle);
    RealmObjectBase.set(this, 'targetByGroup', targetByGroup);
    RealmObjectBase.set(this, 'targetByCompany', targetByCompany);
    RealmObjectBase.set(this, 'targetByRegion', targetByRegion);
    RealmObjectBase.set(this, 'targetByBudget', targetByBudget);
    RealmObjectBase.set(this, 'osaForm', osaForm);
    RealmObjectBase.set(this, 'companyId', companyId);
    RealmObjectBase.set(this, 'itemImage', itemImage);
    RealmObjectBase.set(this, 'targeted', targeted);
  }

  QuestionPartModel._();

  @override
  int? get projectid => RealmObjectBase.get<int>(this, 'projectid') as int?;
  @override
  set projectid(int? value) => RealmObjectBase.set(this, 'projectid', value);

  @override
  int? get questionpartId =>
      RealmObjectBase.get<int>(this, 'questionpartId') as int?;
  @override
  set questionpartId(int? value) =>
      RealmObjectBase.set(this, 'questionpartId', value);

  @override
  String? get questionpartDescription =>
      RealmObjectBase.get<String>(this, 'questionpartDescription') as String?;
  @override
  set questionpartDescription(String? value) =>
      RealmObjectBase.set(this, 'questionpartDescription', value);

  @override
  String? get price => RealmObjectBase.get<String>(this, 'price') as String?;
  @override
  set price(String? value) => RealmObjectBase.set(this, 'price', value);

  @override
  DateTime? get modifiedTimeStampQuestionpart =>
      RealmObjectBase.get<DateTime>(this, 'modifiedTimeStampQuestionpart')
          as DateTime?;
  @override
  set modifiedTimeStampQuestionpart(DateTime? value) =>
      RealmObjectBase.set(this, 'modifiedTimeStampQuestionpart', value);

  @override
  bool? get targetByCycle =>
      RealmObjectBase.get<bool>(this, 'targetByCycle') as bool?;
  @override
  set targetByCycle(bool? value) =>
      RealmObjectBase.set(this, 'targetByCycle', value);

  @override
  bool? get targetByGroup =>
      RealmObjectBase.get<bool>(this, 'targetByGroup') as bool?;
  @override
  set targetByGroup(bool? value) =>
      RealmObjectBase.set(this, 'targetByGroup', value);

  @override
  bool? get targetByCompany =>
      RealmObjectBase.get<bool>(this, 'targetByCompany') as bool?;
  @override
  set targetByCompany(bool? value) =>
      RealmObjectBase.set(this, 'targetByCompany', value);

  @override
  bool? get targetByRegion =>
      RealmObjectBase.get<bool>(this, 'targetByRegion') as bool?;
  @override
  set targetByRegion(bool? value) =>
      RealmObjectBase.set(this, 'targetByRegion', value);

  @override
  bool? get targetByBudget =>
      RealmObjectBase.get<bool>(this, 'targetByBudget') as bool?;
  @override
  set targetByBudget(bool? value) =>
      RealmObjectBase.set(this, 'targetByBudget', value);

  @override
  bool? get osaForm => RealmObjectBase.get<bool>(this, 'osaForm') as bool?;
  @override
  set osaForm(bool? value) => RealmObjectBase.set(this, 'osaForm', value);

  @override
  int? get companyId => RealmObjectBase.get<int>(this, 'companyId') as int?;
  @override
  set companyId(int? value) => RealmObjectBase.set(this, 'companyId', value);

  @override
  String? get itemImage =>
      RealmObjectBase.get<String>(this, 'itemImage') as String?;
  @override
  set itemImage(String? value) => RealmObjectBase.set(this, 'itemImage', value);

  @override
  int? get targeted => RealmObjectBase.get<int>(this, 'targeted') as int?;
  @override
  set targeted(int? value) => RealmObjectBase.set(this, 'targeted', value);

  @override
  Stream<RealmObjectChanges<QuestionPartModel>> get changes =>
      RealmObjectBase.getChanges<QuestionPartModel>(this);

  @override
  Stream<RealmObjectChanges<QuestionPartModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<QuestionPartModel>(this, keyPaths);

  @override
  QuestionPartModel freeze() =>
      RealmObjectBase.freezeObject<QuestionPartModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'projectid': projectid.toEJson(),
      'questionpartId': questionpartId.toEJson(),
      'questionpartDescription': questionpartDescription.toEJson(),
      'price': price.toEJson(),
      'modifiedTimeStampQuestionpart': modifiedTimeStampQuestionpart.toEJson(),
      'targetByCycle': targetByCycle.toEJson(),
      'targetByGroup': targetByGroup.toEJson(),
      'targetByCompany': targetByCompany.toEJson(),
      'targetByRegion': targetByRegion.toEJson(),
      'targetByBudget': targetByBudget.toEJson(),
      'osaForm': osaForm.toEJson(),
      'companyId': companyId.toEJson(),
      'itemImage': itemImage.toEJson(),
      'targeted': targeted.toEJson(),
    };
  }

  static EJsonValue _toEJson(QuestionPartModel value) => value.toEJson();
  static QuestionPartModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return QuestionPartModel(
      projectid: fromEJson(ejson['projectid']),
      questionpartId: fromEJson(ejson['questionpartId']),
      questionpartDescription: fromEJson(ejson['questionpartDescription']),
      price: fromEJson(ejson['price']),
      modifiedTimeStampQuestionpart:
          fromEJson(ejson['modifiedTimeStampQuestionpart']),
      targetByCycle: fromEJson(ejson['targetByCycle']),
      targetByGroup: fromEJson(ejson['targetByGroup']),
      targetByCompany: fromEJson(ejson['targetByCompany']),
      targetByRegion: fromEJson(ejson['targetByRegion']),
      targetByBudget: fromEJson(ejson['targetByBudget']),
      osaForm: fromEJson(ejson['osaForm']),
      companyId: fromEJson(ejson['companyId']),
      itemImage: fromEJson(ejson['itemImage']),
      targeted: fromEJson(ejson['targeted']),
    );
  }

  static final schema = () {
    RealmObjectBase.registerFactory(QuestionPartModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, QuestionPartModel, 'QuestionPartModel', [
      SchemaProperty('projectid', RealmPropertyType.int, optional: true),
      SchemaProperty('questionpartId', RealmPropertyType.int, optional: true),
      SchemaProperty('questionpartDescription', RealmPropertyType.string,
          optional: true),
      SchemaProperty('price', RealmPropertyType.string, optional: true),
      SchemaProperty(
          'modifiedTimeStampQuestionpart', RealmPropertyType.timestamp,
          optional: true),
      SchemaProperty('targetByCycle', RealmPropertyType.bool, optional: true),
      SchemaProperty('targetByGroup', RealmPropertyType.bool, optional: true),
      SchemaProperty('targetByCompany', RealmPropertyType.bool, optional: true),
      SchemaProperty('targetByRegion', RealmPropertyType.bool, optional: true),
      SchemaProperty('targetByBudget', RealmPropertyType.bool, optional: true),
      SchemaProperty('osaForm', RealmPropertyType.bool, optional: true),
      SchemaProperty('companyId', RealmPropertyType.int, optional: true),
      SchemaProperty('itemImage', RealmPropertyType.string, optional: true),
      SchemaProperty('targeted', RealmPropertyType.int, optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class PhotoFolderModel extends _PhotoFolderModel
    with RealmEntity, RealmObjectBase, RealmObject {
  PhotoFolderModel({
    Iterable<PhotoModel> photos = const [],
    bool? attribute,
    int? folderId,
    String? folderName,
    int? folderPictureAmount,
    bool? imageRec,
    DateTime? modifiedTimeStampPhototype,
  }) {
    RealmObjectBase.set<RealmList<PhotoModel>>(
        this, 'photos', RealmList<PhotoModel>(photos));
    RealmObjectBase.set(this, 'attribute', attribute);
    RealmObjectBase.set(this, 'folderId', folderId);
    RealmObjectBase.set(this, 'folderName', folderName);
    RealmObjectBase.set(this, 'folderPictureAmount', folderPictureAmount);
    RealmObjectBase.set(this, 'imageRec', imageRec);
    RealmObjectBase.set(
        this, 'modifiedTimeStampPhototype', modifiedTimeStampPhototype);
  }

  PhotoFolderModel._();

  @override
  RealmList<PhotoModel> get photos =>
      RealmObjectBase.get<PhotoModel>(this, 'photos') as RealmList<PhotoModel>;
  @override
  set photos(covariant RealmList<PhotoModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  bool? get attribute => RealmObjectBase.get<bool>(this, 'attribute') as bool?;
  @override
  set attribute(bool? value) => RealmObjectBase.set(this, 'attribute', value);

  @override
  int? get folderId => RealmObjectBase.get<int>(this, 'folderId') as int?;
  @override
  set folderId(int? value) => RealmObjectBase.set(this, 'folderId', value);

  @override
  String? get folderName =>
      RealmObjectBase.get<String>(this, 'folderName') as String?;
  @override
  set folderName(String? value) =>
      RealmObjectBase.set(this, 'folderName', value);

  @override
  int? get folderPictureAmount =>
      RealmObjectBase.get<int>(this, 'folderPictureAmount') as int?;
  @override
  set folderPictureAmount(int? value) =>
      RealmObjectBase.set(this, 'folderPictureAmount', value);

  @override
  bool? get imageRec => RealmObjectBase.get<bool>(this, 'imageRec') as bool?;
  @override
  set imageRec(bool? value) => RealmObjectBase.set(this, 'imageRec', value);

  @override
  DateTime? get modifiedTimeStampPhototype =>
      RealmObjectBase.get<DateTime>(this, 'modifiedTimeStampPhototype')
          as DateTime?;
  @override
  set modifiedTimeStampPhototype(DateTime? value) =>
      RealmObjectBase.set(this, 'modifiedTimeStampPhototype', value);

  @override
  Stream<RealmObjectChanges<PhotoFolderModel>> get changes =>
      RealmObjectBase.getChanges<PhotoFolderModel>(this);

  @override
  Stream<RealmObjectChanges<PhotoFolderModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<PhotoFolderModel>(this, keyPaths);

  @override
  PhotoFolderModel freeze() =>
      RealmObjectBase.freezeObject<PhotoFolderModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'photos': photos.toEJson(),
      'attribute': attribute.toEJson(),
      'folderId': folderId.toEJson(),
      'folderName': folderName.toEJson(),
      'folderPictureAmount': folderPictureAmount.toEJson(),
      'imageRec': imageRec.toEJson(),
      'modifiedTimeStampPhototype': modifiedTimeStampPhototype.toEJson(),
    };
  }

  static EJsonValue _toEJson(PhotoFolderModel value) => value.toEJson();
  static PhotoFolderModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return PhotoFolderModel(
      photos: fromEJson(ejson['photos']),
      attribute: fromEJson(ejson['attribute']),
      folderId: fromEJson(ejson['folderId']),
      folderName: fromEJson(ejson['folderName']),
      folderPictureAmount: fromEJson(ejson['folderPictureAmount']),
      imageRec: fromEJson(ejson['imageRec']),
      modifiedTimeStampPhototype:
          fromEJson(ejson['modifiedTimeStampPhototype']),
    );
  }

  static final schema = () {
    RealmObjectBase.registerFactory(PhotoFolderModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, PhotoFolderModel, 'PhotoFolderModel', [
      SchemaProperty('photos', RealmPropertyType.object,
          linkTarget: 'PhotoModel', collectionType: RealmCollectionType.list),
      SchemaProperty('attribute', RealmPropertyType.bool, optional: true),
      SchemaProperty('folderId', RealmPropertyType.int, optional: true),
      SchemaProperty('folderName', RealmPropertyType.string, optional: true),
      SchemaProperty('folderPictureAmount', RealmPropertyType.int,
          optional: true),
      SchemaProperty('imageRec', RealmPropertyType.bool, optional: true),
      SchemaProperty('modifiedTimeStampPhototype', RealmPropertyType.timestamp,
          optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class PosItemModel extends _PosItemModel
    with RealmEntity, RealmObjectBase, RealmObject {
  PosItemModel({
    String? itemName,
    int? itemAmount,
    String? photoUrl,
  }) {
    RealmObjectBase.set(this, 'itemName', itemName);
    RealmObjectBase.set(this, 'itemAmount', itemAmount);
    RealmObjectBase.set(this, 'photoUrl', photoUrl);
  }

  PosItemModel._();

  @override
  String? get itemName =>
      RealmObjectBase.get<String>(this, 'itemName') as String?;
  @override
  set itemName(String? value) => RealmObjectBase.set(this, 'itemName', value);

  @override
  int? get itemAmount => RealmObjectBase.get<int>(this, 'itemAmount') as int?;
  @override
  set itemAmount(int? value) => RealmObjectBase.set(this, 'itemAmount', value);

  @override
  String? get photoUrl =>
      RealmObjectBase.get<String>(this, 'photoUrl') as String?;
  @override
  set photoUrl(String? value) => RealmObjectBase.set(this, 'photoUrl', value);

  @override
  Stream<RealmObjectChanges<PosItemModel>> get changes =>
      RealmObjectBase.getChanges<PosItemModel>(this);

  @override
  Stream<RealmObjectChanges<PosItemModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<PosItemModel>(this, keyPaths);

  @override
  PosItemModel freeze() => RealmObjectBase.freezeObject<PosItemModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'itemName': itemName.toEJson(),
      'itemAmount': itemAmount.toEJson(),
      'photoUrl': photoUrl.toEJson(),
    };
  }

  static EJsonValue _toEJson(PosItemModel value) => value.toEJson();
  static PosItemModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return PosItemModel(
      itemName: fromEJson(ejson['itemName']),
      itemAmount: fromEJson(ejson['itemAmount']),
      photoUrl: fromEJson(ejson['photoUrl']),
    );
  }

  static final schema = () {
    RealmObjectBase.registerFactory(PosItemModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, PosItemModel, 'PosItemModel', [
      SchemaProperty('itemName', RealmPropertyType.string, optional: true),
      SchemaProperty('itemAmount', RealmPropertyType.int, optional: true),
      SchemaProperty('photoUrl', RealmPropertyType.string, optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class SignatureFolderModel extends _SignatureFolderModel
    with RealmEntity, RealmObjectBase, RealmObject {
  SignatureFolderModel({
    Iterable<SignatureModel> signatures = const [],
    bool? attribute,
    int? folderId,
    String? folderName,
    DateTime? modifiedTimeStampSignaturetype,
  }) {
    RealmObjectBase.set<RealmList<SignatureModel>>(
        this, 'signatures', RealmList<SignatureModel>(signatures));
    RealmObjectBase.set(this, 'attribute', attribute);
    RealmObjectBase.set(this, 'folderId', folderId);
    RealmObjectBase.set(this, 'folderName', folderName);
    RealmObjectBase.set(
        this, 'modifiedTimeStampSignaturetype', modifiedTimeStampSignaturetype);
  }

  SignatureFolderModel._();

  @override
  RealmList<SignatureModel> get signatures =>
      RealmObjectBase.get<SignatureModel>(this, 'signatures')
          as RealmList<SignatureModel>;
  @override
  set signatures(covariant RealmList<SignatureModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  bool? get attribute => RealmObjectBase.get<bool>(this, 'attribute') as bool?;
  @override
  set attribute(bool? value) => RealmObjectBase.set(this, 'attribute', value);

  @override
  int? get folderId => RealmObjectBase.get<int>(this, 'folderId') as int?;
  @override
  set folderId(int? value) => RealmObjectBase.set(this, 'folderId', value);

  @override
  String? get folderName =>
      RealmObjectBase.get<String>(this, 'folderName') as String?;
  @override
  set folderName(String? value) =>
      RealmObjectBase.set(this, 'folderName', value);

  @override
  DateTime? get modifiedTimeStampSignaturetype =>
      RealmObjectBase.get<DateTime>(this, 'modifiedTimeStampSignaturetype')
          as DateTime?;
  @override
  set modifiedTimeStampSignaturetype(DateTime? value) =>
      RealmObjectBase.set(this, 'modifiedTimeStampSignaturetype', value);

  @override
  Stream<RealmObjectChanges<SignatureFolderModel>> get changes =>
      RealmObjectBase.getChanges<SignatureFolderModel>(this);

  @override
  Stream<RealmObjectChanges<SignatureFolderModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<SignatureFolderModel>(this, keyPaths);

  @override
  SignatureFolderModel freeze() =>
      RealmObjectBase.freezeObject<SignatureFolderModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'signatures': signatures.toEJson(),
      'attribute': attribute.toEJson(),
      'folderId': folderId.toEJson(),
      'folderName': folderName.toEJson(),
      'modifiedTimeStampSignaturetype':
          modifiedTimeStampSignaturetype.toEJson(),
    };
  }

  static EJsonValue _toEJson(SignatureFolderModel value) => value.toEJson();
  static SignatureFolderModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return SignatureFolderModel(
      signatures: fromEJson(ejson['signatures']),
      attribute: fromEJson(ejson['attribute']),
      folderId: fromEJson(ejson['folderId']),
      folderName: fromEJson(ejson['folderName']),
      modifiedTimeStampSignaturetype:
          fromEJson(ejson['modifiedTimeStampSignaturetype']),
    );
  }

  static final schema = () {
    RealmObjectBase.registerFactory(SignatureFolderModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, SignatureFolderModel, 'SignatureFolderModel', [
      SchemaProperty('signatures', RealmPropertyType.object,
          linkTarget: 'SignatureModel',
          collectionType: RealmCollectionType.list),
      SchemaProperty('attribute', RealmPropertyType.bool, optional: true),
      SchemaProperty('folderId', RealmPropertyType.int, optional: true),
      SchemaProperty('folderName', RealmPropertyType.string, optional: true),
      SchemaProperty(
          'modifiedTimeStampSignaturetype', RealmPropertyType.timestamp,
          optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class SignatureModel extends _SignatureModel
    with RealmEntity, RealmObjectBase, RealmObject {
  SignatureModel({
    int? signatureId,
    int? formId,
    int? questionId,
    String? signatureUrl,
    String? thumbnailUrl,
    String? signedBy,
    DateTime? modifiedTimeStampSignature,
    bool? userDeletedSignature,
    bool? cannotUploadMandatory,
  }) {
    RealmObjectBase.set(this, 'signatureId', signatureId);
    RealmObjectBase.set(this, 'formId', formId);
    RealmObjectBase.set(this, 'questionId', questionId);
    RealmObjectBase.set(this, 'signatureUrl', signatureUrl);
    RealmObjectBase.set(this, 'thumbnailUrl', thumbnailUrl);
    RealmObjectBase.set(this, 'signedBy', signedBy);
    RealmObjectBase.set(
        this, 'modifiedTimeStampSignature', modifiedTimeStampSignature);
    RealmObjectBase.set(this, 'userDeletedSignature', userDeletedSignature);
    RealmObjectBase.set(this, 'cannotUploadMandatory', cannotUploadMandatory);
  }

  SignatureModel._();

  @override
  int? get signatureId => RealmObjectBase.get<int>(this, 'signatureId') as int?;
  @override
  set signatureId(int? value) =>
      RealmObjectBase.set(this, 'signatureId', value);

  @override
  int? get formId => RealmObjectBase.get<int>(this, 'formId') as int?;
  @override
  set formId(int? value) => RealmObjectBase.set(this, 'formId', value);

  @override
  int? get questionId => RealmObjectBase.get<int>(this, 'questionId') as int?;
  @override
  set questionId(int? value) => RealmObjectBase.set(this, 'questionId', value);

  @override
  String? get signatureUrl =>
      RealmObjectBase.get<String>(this, 'signatureUrl') as String?;
  @override
  set signatureUrl(String? value) =>
      RealmObjectBase.set(this, 'signatureUrl', value);

  @override
  String? get thumbnailUrl =>
      RealmObjectBase.get<String>(this, 'thumbnailUrl') as String?;
  @override
  set thumbnailUrl(String? value) =>
      RealmObjectBase.set(this, 'thumbnailUrl', value);

  @override
  String? get signedBy =>
      RealmObjectBase.get<String>(this, 'signedBy') as String?;
  @override
  set signedBy(String? value) => RealmObjectBase.set(this, 'signedBy', value);

  @override
  DateTime? get modifiedTimeStampSignature =>
      RealmObjectBase.get<DateTime>(this, 'modifiedTimeStampSignature')
          as DateTime?;
  @override
  set modifiedTimeStampSignature(DateTime? value) =>
      RealmObjectBase.set(this, 'modifiedTimeStampSignature', value);

  @override
  bool? get userDeletedSignature =>
      RealmObjectBase.get<bool>(this, 'userDeletedSignature') as bool?;
  @override
  set userDeletedSignature(bool? value) =>
      RealmObjectBase.set(this, 'userDeletedSignature', value);

  @override
  bool? get cannotUploadMandatory =>
      RealmObjectBase.get<bool>(this, 'cannotUploadMandatory') as bool?;
  @override
  set cannotUploadMandatory(bool? value) =>
      RealmObjectBase.set(this, 'cannotUploadMandatory', value);

  @override
  Stream<RealmObjectChanges<SignatureModel>> get changes =>
      RealmObjectBase.getChanges<SignatureModel>(this);

  @override
  Stream<RealmObjectChanges<SignatureModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<SignatureModel>(this, keyPaths);

  @override
  SignatureModel freeze() => RealmObjectBase.freezeObject<SignatureModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'signatureId': signatureId.toEJson(),
      'formId': formId.toEJson(),
      'questionId': questionId.toEJson(),
      'signatureUrl': signatureUrl.toEJson(),
      'thumbnailUrl': thumbnailUrl.toEJson(),
      'signedBy': signedBy.toEJson(),
      'modifiedTimeStampSignature': modifiedTimeStampSignature.toEJson(),
      'userDeletedSignature': userDeletedSignature.toEJson(),
      'cannotUploadMandatory': cannotUploadMandatory.toEJson(),
    };
  }

  static EJsonValue _toEJson(SignatureModel value) => value.toEJson();
  static SignatureModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return SignatureModel(
      signatureId: fromEJson(ejson['signatureId']),
      formId: fromEJson(ejson['formId']),
      questionId: fromEJson(ejson['questionId']),
      signatureUrl: fromEJson(ejson['signatureUrl']),
      thumbnailUrl: fromEJson(ejson['thumbnailUrl']),
      signedBy: fromEJson(ejson['signedBy']),
      modifiedTimeStampSignature:
          fromEJson(ejson['modifiedTimeStampSignature']),
      userDeletedSignature: fromEJson(ejson['userDeletedSignature']),
      cannotUploadMandatory: fromEJson(ejson['cannotUploadMandatory']),
    );
  }

  static final schema = () {
    RealmObjectBase.registerFactory(SignatureModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, SignatureModel, 'SignatureModel', [
      SchemaProperty('signatureId', RealmPropertyType.int, optional: true),
      SchemaProperty('formId', RealmPropertyType.int, optional: true),
      SchemaProperty('questionId', RealmPropertyType.int, optional: true),
      SchemaProperty('signatureUrl', RealmPropertyType.string, optional: true),
      SchemaProperty('thumbnailUrl', RealmPropertyType.string, optional: true),
      SchemaProperty('signedBy', RealmPropertyType.string, optional: true),
      SchemaProperty('modifiedTimeStampSignature', RealmPropertyType.timestamp,
          optional: true),
      SchemaProperty('userDeletedSignature', RealmPropertyType.bool,
          optional: true),
      SchemaProperty('cannotUploadMandatory', RealmPropertyType.bool,
          optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class StocktakeModel extends _StocktakeModel
    with RealmEntity, RealmObjectBase, RealmObject {
  StocktakeModel({
    int? taskId,
    int? itemId,
    String? itemCode,
    String? itemName,
    String? itemGroup,
    String? imageUrl,
    String? itemLocation,
    int? itemQty,
  }) {
    RealmObjectBase.set(this, 'taskId', taskId);
    RealmObjectBase.set(this, 'itemId', itemId);
    RealmObjectBase.set(this, 'itemCode', itemCode);
    RealmObjectBase.set(this, 'itemName', itemName);
    RealmObjectBase.set(this, 'itemGroup', itemGroup);
    RealmObjectBase.set(this, 'imageUrl', imageUrl);
    RealmObjectBase.set(this, 'itemLocation', itemLocation);
    RealmObjectBase.set(this, 'itemQty', itemQty);
  }

  StocktakeModel._();

  @override
  int? get taskId => RealmObjectBase.get<int>(this, 'taskId') as int?;
  @override
  set taskId(int? value) => RealmObjectBase.set(this, 'taskId', value);

  @override
  int? get itemId => RealmObjectBase.get<int>(this, 'itemId') as int?;
  @override
  set itemId(int? value) => RealmObjectBase.set(this, 'itemId', value);

  @override
  String? get itemCode =>
      RealmObjectBase.get<String>(this, 'itemCode') as String?;
  @override
  set itemCode(String? value) => RealmObjectBase.set(this, 'itemCode', value);

  @override
  String? get itemName =>
      RealmObjectBase.get<String>(this, 'itemName') as String?;
  @override
  set itemName(String? value) => RealmObjectBase.set(this, 'itemName', value);

  @override
  String? get itemGroup =>
      RealmObjectBase.get<String>(this, 'itemGroup') as String?;
  @override
  set itemGroup(String? value) => RealmObjectBase.set(this, 'itemGroup', value);

  @override
  String? get imageUrl =>
      RealmObjectBase.get<String>(this, 'imageUrl') as String?;
  @override
  set imageUrl(String? value) => RealmObjectBase.set(this, 'imageUrl', value);

  @override
  String? get itemLocation =>
      RealmObjectBase.get<String>(this, 'itemLocation') as String?;
  @override
  set itemLocation(String? value) =>
      RealmObjectBase.set(this, 'itemLocation', value);

  @override
  int? get itemQty => RealmObjectBase.get<int>(this, 'itemQty') as int?;
  @override
  set itemQty(int? value) => RealmObjectBase.set(this, 'itemQty', value);

  @override
  Stream<RealmObjectChanges<StocktakeModel>> get changes =>
      RealmObjectBase.getChanges<StocktakeModel>(this);

  @override
  Stream<RealmObjectChanges<StocktakeModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<StocktakeModel>(this, keyPaths);

  @override
  StocktakeModel freeze() => RealmObjectBase.freezeObject<StocktakeModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'taskId': taskId.toEJson(),
      'itemId': itemId.toEJson(),
      'itemCode': itemCode.toEJson(),
      'itemName': itemName.toEJson(),
      'itemGroup': itemGroup.toEJson(),
      'imageUrl': imageUrl.toEJson(),
      'itemLocation': itemLocation.toEJson(),
      'itemQty': itemQty.toEJson(),
    };
  }

  static EJsonValue _toEJson(StocktakeModel value) => value.toEJson();
  static StocktakeModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return StocktakeModel(
      taskId: fromEJson(ejson['taskId']),
      itemId: fromEJson(ejson['itemId']),
      itemCode: fromEJson(ejson['itemCode']),
      itemName: fromEJson(ejson['itemName']),
      itemGroup: fromEJson(ejson['itemGroup']),
      imageUrl: fromEJson(ejson['imageUrl']),
      itemLocation: fromEJson(ejson['itemLocation']),
      itemQty: fromEJson(ejson['itemQty']),
    );
  }

  static final schema = () {
    RealmObjectBase.registerFactory(StocktakeModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, StocktakeModel, 'StocktakeModel', [
      SchemaProperty('taskId', RealmPropertyType.int, optional: true),
      SchemaProperty('itemId', RealmPropertyType.int, optional: true),
      SchemaProperty('itemCode', RealmPropertyType.string, optional: true),
      SchemaProperty('itemName', RealmPropertyType.string, optional: true),
      SchemaProperty('itemGroup', RealmPropertyType.string, optional: true),
      SchemaProperty('imageUrl', RealmPropertyType.string, optional: true),
      SchemaProperty('itemLocation', RealmPropertyType.string, optional: true),
      SchemaProperty('itemQty', RealmPropertyType.int, optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class TaskalertModel extends _TaskalertModel
    with RealmEntity, RealmObjectBase, RealmObject {
  TaskalertModel({
    int? messageId,
    int? schedulepeopleid,
    String? subject,
    String? message,
  }) {
    RealmObjectBase.set(this, 'messageId', messageId);
    RealmObjectBase.set(this, 'schedulepeopleid', schedulepeopleid);
    RealmObjectBase.set(this, 'subject', subject);
    RealmObjectBase.set(this, 'message', message);
  }

  TaskalertModel._();

  @override
  int? get messageId => RealmObjectBase.get<int>(this, 'messageId') as int?;
  @override
  set messageId(int? value) => RealmObjectBase.set(this, 'messageId', value);

  @override
  int? get schedulepeopleid =>
      RealmObjectBase.get<int>(this, 'schedulepeopleid') as int?;
  @override
  set schedulepeopleid(int? value) =>
      RealmObjectBase.set(this, 'schedulepeopleid', value);

  @override
  String? get subject =>
      RealmObjectBase.get<String>(this, 'subject') as String?;
  @override
  set subject(String? value) => RealmObjectBase.set(this, 'subject', value);

  @override
  String? get message =>
      RealmObjectBase.get<String>(this, 'message') as String?;
  @override
  set message(String? value) => RealmObjectBase.set(this, 'message', value);

  @override
  Stream<RealmObjectChanges<TaskalertModel>> get changes =>
      RealmObjectBase.getChanges<TaskalertModel>(this);

  @override
  Stream<RealmObjectChanges<TaskalertModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<TaskalertModel>(this, keyPaths);

  @override
  TaskalertModel freeze() => RealmObjectBase.freezeObject<TaskalertModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'messageId': messageId.toEJson(),
      'schedulepeopleid': schedulepeopleid.toEJson(),
      'subject': subject.toEJson(),
      'message': message.toEJson(),
    };
  }

  static EJsonValue _toEJson(TaskalertModel value) => value.toEJson();
  static TaskalertModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return TaskalertModel(
      messageId: fromEJson(ejson['messageId']),
      schedulepeopleid: fromEJson(ejson['schedulepeopleid']),
      subject: fromEJson(ejson['subject']),
      message: fromEJson(ejson['message']),
    );
  }

  static final schema = () {
    RealmObjectBase.registerFactory(TaskalertModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, TaskalertModel, 'TaskalertModel', [
      SchemaProperty('messageId', RealmPropertyType.int, optional: true),
      SchemaProperty('schedulepeopleid', RealmPropertyType.int, optional: true),
      SchemaProperty('subject', RealmPropertyType.string, optional: true),
      SchemaProperty('message', RealmPropertyType.string, optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class TaskmemberModel extends _TaskmemberModel
    with RealmEntity, RealmObjectBase, RealmObject {
  TaskmemberModel({
    String? fullname,
    int? teamLead,
    String? email,
    int? scheduleId,
    int? taskId,
  }) {
    RealmObjectBase.set(this, 'fullname', fullname);
    RealmObjectBase.set(this, 'teamLead', teamLead);
    RealmObjectBase.set(this, 'email', email);
    RealmObjectBase.set(this, 'scheduleId', scheduleId);
    RealmObjectBase.set(this, 'taskId', taskId);
  }

  TaskmemberModel._();

  @override
  String? get fullname =>
      RealmObjectBase.get<String>(this, 'fullname') as String?;
  @override
  set fullname(String? value) => RealmObjectBase.set(this, 'fullname', value);

  @override
  int? get teamLead => RealmObjectBase.get<int>(this, 'teamLead') as int?;
  @override
  set teamLead(int? value) => RealmObjectBase.set(this, 'teamLead', value);

  @override
  String? get email => RealmObjectBase.get<String>(this, 'email') as String?;
  @override
  set email(String? value) => RealmObjectBase.set(this, 'email', value);

  @override
  int? get scheduleId => RealmObjectBase.get<int>(this, 'scheduleId') as int?;
  @override
  set scheduleId(int? value) => RealmObjectBase.set(this, 'scheduleId', value);

  @override
  int? get taskId => RealmObjectBase.get<int>(this, 'taskId') as int?;
  @override
  set taskId(int? value) => RealmObjectBase.set(this, 'taskId', value);

  @override
  Stream<RealmObjectChanges<TaskmemberModel>> get changes =>
      RealmObjectBase.getChanges<TaskmemberModel>(this);

  @override
  Stream<RealmObjectChanges<TaskmemberModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<TaskmemberModel>(this, keyPaths);

  @override
  TaskmemberModel freeze() =>
      RealmObjectBase.freezeObject<TaskmemberModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'fullname': fullname.toEJson(),
      'teamLead': teamLead.toEJson(),
      'email': email.toEJson(),
      'scheduleId': scheduleId.toEJson(),
      'taskId': taskId.toEJson(),
    };
  }

  static EJsonValue _toEJson(TaskmemberModel value) => value.toEJson();
  static TaskmemberModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return TaskmemberModel(
      fullname: fromEJson(ejson['fullname']),
      teamLead: fromEJson(ejson['teamLead']),
      email: fromEJson(ejson['email']),
      scheduleId: fromEJson(ejson['scheduleId']),
      taskId: fromEJson(ejson['taskId']),
    );
  }

  static final schema = () {
    RealmObjectBase.registerFactory(TaskmemberModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, TaskmemberModel, 'TaskmemberModel', [
      SchemaProperty('fullname', RealmPropertyType.string, optional: true),
      SchemaProperty('teamLead', RealmPropertyType.int, optional: true),
      SchemaProperty('email', RealmPropertyType.string, optional: true),
      SchemaProperty('scheduleId', RealmPropertyType.int, optional: true),
      SchemaProperty('taskId', RealmPropertyType.int, optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}
