import 'package:realm/realm.dart';
part 'task_detail_model.realm.dart';

// TaskDetailModel with all fields and Realm support
@RealmModel()
abstract class _TaskDetailModel {
  @PrimaryKey()
  int id = 0;
  bool isSynced = false;

  int? taskId;
  int? projectId;
  int? scheduleId;
  bool? sentToPayroll;
  bool? showKm;
  int? storeId;
  String? client;
  int? clientId;
  String? clientLogoUrl;
  String? storeGroup;
  int? storeGroupId;
  String? storeName;
  String? storeEmail;
  int? minutes;
  int? budget;
  int? originalbudget;
  String? comment;
  int? claimableKms;
  int? flightDuration;
  int? pages;
  String? location;
  String? suburb;
  double? latitude;
  double? longitude;
  double? taskLatitude;
  double? taskLongitude;
  String? cycle;
  int? cycleId;
  bool? canDelete;
  DateTime? scheduledTimeStamp;
  DateTime? submissionTimeStamp;
  DateTime? expires;
  String? onTask;
  String? phone;
  DateTime? rangeStart;
  DateTime? rangeEnd;
  bool? reOpened;
  String? reOpenedReason;
  String? taskStatus;
  int? warehousejobId;
  String? connoteUrl;
  bool? posRequired;
  bool? isPosMandatory;
  String? posReceived;
  late List<_PhotoFolderModel> photoFolder;
  late List<_SignatureFolderModel> signatureFolder;
  late List<_FormModel> forms;
  late List<_PosItemModel> posItems;
  late List<_DocumentModel> documents;
  late List<_TaskalertModel> taskalerts;
  late List<_TaskmemberModel> taskmembers;
  late List<_PhotoTagsTModel> photoTagsTwo;
  late List<_PhotoTagsTModel> photoTagsThree;
  DateTime? modifiedTimeStampDocuments;
  DateTime? modifiedTimeStampForms;
  DateTime? modifiedTimeStampMembers;
  DateTime? modifiedTimeStampTask;
  DateTime? modifiedTimeStampPhotos;
  DateTime? modifiedTimeStampSignatures;
  DateTime? modifiedTimeStampSignaturetypes;
  String? posSentTo;
  String? posSentToEmail;
  DateTime? modifiedTimeStampPhototypes;
  DateTime? taskCommencementTimeStamp;
  DateTime? taskStoppedTimeStamp;
  int? teamlead;
  late List<_FollowupTaskModel> followupTasks;
  late List<_StocktakeModel> stocktake;
  String? taskNote;
  bool? disallowReschedule;
  int? photoResPerc;
  bool? liveImagesOnly;
  String? timeSchedule;
  int? scheduleTypeId;
  bool? showFollowupIconMulti;
  bool? followupSelectedMulti;
  int? regionId;
  bool? isOpen;
  int? taskCount;
  int? ctFormsTotalCnt;
  int? ctFormsCompletedCnt;
  String? preftime;
  bool? sendTo;
  int? kTotal;
  int? kCompleted;
}

@RealmModel()
abstract class _DocumentModel {
  int? projectId;
  int? documentId;
  int? documentTypeId;
  String? documentName;
  String? documentIconLink;
  late List<_FileElementModel> files;
  DateTime? modifiedTimeStampDocument;
}

@RealmModel()
abstract class _FileElementModel {
  int? documentId;
  int? projectId;
  String? documentFileLink;
  int? fileId;
  DateTime? modifiedTimeStampFile;
}

@RealmModel()
abstract class _FollowupTaskModel {
  bool? showFollowupIcon;
  bool? followupSelected;
  DateTime? selectedVisitDate;
  int? selectedFollowupTypeId;
  int? selectedFollowupItemId;
  int? selectedBudget;
  String? selectedFollowupType;
  String? selectedFollowupItem;
  String? selectedScheduleNote;
  int? followupNumber;
}

@RealmModel()
abstract class _FormModel {
  int? formId;
  int? formInstanceId;
  String? formName;
  String? briefUrl;
  late List<_QuestionModel> questions;
  late List<_QuestionAnswerModel> questionAnswers;
  DateTime? modifiedTimeStampForm;
  DateTime? dateStart;
  bool? isVisionForm;
  String? visionFormUrl;
  bool? isMandatory;
  bool? formPreview;
  int? formTypeId;
  bool? formCompleted;
  bool? formAllowForward;
  bool? formShowPrice;
  int? minQty;
  bool? showQuestions;
  int? kTotal;
  int? kCompleted;
}

@RealmModel()
abstract class _QuestionAnswerModel {
  int? taskId;
  int? formId;
  int? questionId;
  int? questionpartId;
  bool? flip;
  String? questionPartMultiId;
  int? measurementId;
  int? measurementTypeId;
  int? measurementOptionId;
  String? measurementOptionIds;
  String? measurementTextResult;
  bool? isComment;
  int? commentTypeId;
}

@RealmModel()
abstract class _QuestionModel {
  int? questionId;
  int? questionOrderId;
  String? questionDescription;
  bool? isComment;
  bool? isCommentMandatory;
  bool? showQuestions;
  bool? hasSignature;
  bool? isSignatureMandatory;
  String? signatureUrl;
  String? photoUrl;
  String? questionBrief;
  late List<_QuestionPartModel> questionParts;
  late List<_MeasurementModel> measurements;
  late List<_QuestionConditionModel> questionConditions;
  late List<_CommentTypeModel> commentTypes;
  DateTime? modifiedTimeStampQuestion;
  bool? targetByCycle;
  bool? targetByGroup;
  bool? targetByCompany;
  bool? targetByRegion;
  bool? targetByBudget;
  bool? isMll;
  late List<_PhotoTagsTModel> photoTagsTwo;
  late List<_PhotoTagsTModel> photoTagsThree;
  bool? isMulti;
  int? multiMeasurementId;
  bool? isMultiOneAnswer;
  bool? flip;
  int? questionTypeId;
}

@RealmModel()
abstract class _CommentTypeModel {
  int? commentTypeId;
  String? commentType;
}

@RealmModel()
abstract class _MeasurementModel {
  int? measurementId;
  int? measurementTypeId;
  String? measurementDescription;
  String? measurementTypeName;
  String? defaultAction;
  late List<_MeasurementOptionModel> measurementOptions;
  late List<_MeasurementConditionModel> measurementConditions;
  late List<_MeasurementConditionModel> measurementConditionsMultiple;
  late List<_MeasurementValidationModel> measurementValidations;
  String? measurementDefaultsResult;
  DateTime? modifiedTimeStampMeasurement;
  DateTime? modifiedTimeStampMeasurementDefaultsResult;
  int? measurementOrderId;
  int? mandatoryPhototypesCount;
  int? optionalPhototypesCount;
  String? measurementImage;
  int? companyid;
  late List<_MeasurementPhototypesDeprecatedModel>
      measurementPhototypesDeprecated;
  DateTime? modifiedTimeStampMeasurementvalidation;
  int? validationTypeId;
  bool? required;
  String? rangeValidation;
  String? expressionValidation;
  String? errorMessage;
  DateTime? modifiedTimeStampMeasurementdefault;
}

@RealmModel()
abstract class _MeasurementConditionModel {
  int? measurementId;
  int? measurementOptionId;
  int? actionMeasurementId;
  String? action;
  DateTime? modifiedTimeStampMeasurementconidtion;
}

@RealmModel()
abstract class _MeasurementOptionModel {
  int? measurementId;
  int? measurementOptionId;
  String? measurementOptionDescription;
  DateTime? modifiedTimeStampMeasurementoption;
  int? budgetOffset;
  int? budgetOffsetType;
  bool? isAnswer;
}

@RealmModel()
abstract class _MeasurementPhototypesDeprecatedModel {
  int? uploadedPictureAmount;
  bool? uploadedBlank;
  bool? uploadedAndCompleted;
  late List<_PhotoModel> photos;
  bool? attribute;
  int? folderId;
  String? folderName;
  int? folderPictureAmount;
  bool? imageRec;
  DateTime? modifiedTimeStampPhototype;
}

@RealmModel()
abstract class _PhotoModel {
  int? formId;
  int? questionId;
  int? measurementId;
  int? folderId;
  int? photoId;
  String? photoUrl;
  String? thumbnailUrl;
  String? caption;
  DateTime? modifiedTimeStampPhoto;
  bool? cannotUploadMandatory;
  bool? userDeletedPhoto;
  bool? imageRec;
  int? questionpartId;
  String? questionPartMultiId;
  int? measurementPhototypeId;
  int? combineTypeId;
  int? photoTagId;
  int? photoCombinetypeId;
}

@RealmModel()
abstract class _MeasurementValidationModel {
  int? measurementId;
  int? validationTypeId;
  bool? required;
  String? rangeValidation;
  String? expressionValidation;
  String? errorMessage;
  DateTime? modifiedTimeStampMeasurementvalidation;
}

@RealmModel()
abstract class _PhotoTagsTModel {
  int? questionpartId;
  int? measurementId;
  bool? isMandatory;
  int? photoResPerc;
  bool? liveImagesOnly;
  int? photoTagId;
  String? photoTag;
  int? numberOfPhotos;
  int? measurementPhototypeId;
  bool? imageRec;
  late List<_PhotoModel> userPhotos;
}

@RealmModel()
abstract class _QuestionConditionModel {
  int? measurementId;
  int? measurementOptionId;
  int? actionQuestionId;
  String? action;
  DateTime? modifiedTimeStampQuestioncondition;
}

@RealmModel()
abstract class _QuestionPartModel {
  int? projectid;
  int? questionpartId;
  String? questionpartDescription;
  String? price;
  DateTime? modifiedTimeStampQuestionpart;
  bool? targetByCycle;
  bool? targetByGroup;
  bool? targetByCompany;
  bool? targetByRegion;
  bool? targetByBudget;
  bool? osaForm;
  int? companyId;
  String? itemImage;
  int? targeted;
}

@RealmModel()
abstract class _PhotoFolderModel {
  late List<_PhotoModel> photos;
  bool? attribute;
  int? folderId;
  String? folderName;
  int? folderPictureAmount;
  bool? imageRec;
  DateTime? modifiedTimeStampPhototype;
}

@RealmModel()
abstract class _PosItemModel {
  String? itemName;
  int? itemAmount;
  String? photoUrl;
}

@RealmModel()
abstract class _SignatureFolderModel {
  late List<_SignatureModel> signatures;
  bool? attribute;
  int? folderId;
  String? folderName;
  DateTime? modifiedTimeStampSignaturetype;
}

@RealmModel()
abstract class _SignatureModel {
  int? signatureId;
  int? formId;
  int? questionId;
  String? signatureUrl;
  String? thumbnailUrl;
  String? signedBy;
  DateTime? modifiedTimeStampSignature;
  bool? userDeletedSignature;
  bool? cannotUploadMandatory;
}

@RealmModel()
abstract class _StocktakeModel {
  int? taskId;
  int? itemId;
  String? itemCode;
  String? itemName;
  String? itemGroup;
  String? imageUrl;
  String? itemLocation;
  int? itemQty;
}

@RealmModel()
abstract class _TaskalertModel {
  int? messageId;
  int? schedulepeopleid;
  String? subject;
  String? message;
}

@RealmModel()
abstract class _TaskmemberModel {
  String? fullname;
  int? teamLead;
  String? email;
  int? scheduleId;
  int? taskId;
}
