import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/features/auth/presentation/widgets/app_button.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
import 'package:storetrack_app/shared/widgets/confirm_dialog.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import '../widgets/pos_item_card.dart';

@RoutePage()
class PosPage extends StatefulWidget {
  final TaskDetail? task;

  const PosPage({
    super.key,
    this.task,
  });

  @override
  State<PosPage> createState() => _PosPageState();
}

class _PosPageState extends State<PosPage> {
  List<PosItem> posItems = [];
  String connoteValue = '';
  bool? posReceived; // null = no status, true = received, false = not received
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    // Initialize with task data if available
    if (widget.task != null) {
      posItems = widget.task!.posItems ?? [];
      connoteValue = widget.task!.connoteUrl ?? 'N/A';
      // Get POS received status from task
      posReceived = _getPosReceivedStatus();

      // Show error dialog if no POS items found
      if (posItems.isEmpty) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _showNoPosItemsDialog();
        });
      }
    } else {
      // Sample data for demonstration when no task is provided
      connoteValue = 'https://www.google.com';
      posItems = [
        PosItem(itemName: 'Sample Item 1', itemAmount: 5, photoUrl: null),
        PosItem(itemName: 'Sample Item 2', itemAmount: 3, photoUrl: null),
        PosItem(itemName: 'Sample Item 3', itemAmount: 8, photoUrl: null),
      ];
      posReceived = null; // No status for sample data
    }
  }

  bool? _getPosReceivedStatus() {
    // Convert string to boolean based on the task's posReceived field
    if (widget.task?.posReceived == null) return null;
    final posReceivedStr = widget.task!.posReceived.toString().toLowerCase();
    if (posReceivedStr == 'true' || posReceivedStr == '1') return true;
    if (posReceivedStr == 'false' || posReceivedStr == '0') return false;
    return null;
  }

  void _showNoPosItemsDialog() {
    ConfirmDialog.show(
      context: context,
      title: 'No POS Items Found',
      message: 'Please try again later.',
      confirmText: 'OK',
      onConfirm: () {
        Navigator.of(context).pop(); // Close POS page
      },
    );
  }

  void _onTrackingPressed() {
    // Open web browser with connote URL for tracking
    if (connoteValue.isNotEmpty && connoteValue != 'N/A') {
      try {
        // Navigate to WebBrowserPage with the connote URL
        context.router.push(WebBrowserRoute(url: connoteValue));
      } catch (e) {
        // Fallback for testing or when router is not available
        SnackBarService.info(
          context: context,
          message: 'Opening tracking for: $connoteValue',
        );
      }
    } else {
      SnackBarService.warning(
        context: context,
        message: 'No tracking URL available',
      );
    }
  }

  void _onReceiveAllPressed() {
    setState(() {
      isLoading = true;
    });

    // Update POS received status to true
    _updatePosStatus(true);
  }

  void _onNotReceivedPressed() {
    setState(() {
      isLoading = true;
    });

    // Update POS received status to false
    _updatePosStatus(false);
  }

  void _updatePosStatus(bool received) {
    // Simulate API call delay
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          posReceived = received;
          isLoading = false;
        });

        // TODO: Implement actual API call to update POS data
        // API.getInstance(context).updatePosData(userId);

        if (received) {
          SnackBarService.success(
            context: context,
            message: 'POS marked as received',
          );
        } else {
          SnackBarService.error(
            context: context,
            message: 'POS marked as not received',
          );
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      backgroundColor: AppColors.lightGrey1,
      appBar: CustomAppBar(
        title: 'POS details',
        onBackPressed: () => Navigator.pop(context),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildShipmentDetailsHeading(textTheme),
            _buildConnoteSection(textTheme),
            _buildActionButtons(),
            _buildPosItemsHeading(textTheme),
            _buildTableHeader(textTheme),
            _buildPosItemsList(),
            const SizedBox(height: 32), // Bottom spacing
          ],
        ),
      ),
    );
  }

  Widget _buildShipmentDetailsHeading(TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 20, 16, 12),
      child: Text(
        'Shipment Details',
        style: textTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildPosItemsHeading(TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 12),
      child: Text(
        'POS Items',
        style: textTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildConnoteSection(TextTheme textTheme) {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black10,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Connote Number:',
                style: textTheme.montserratParagraphSmall.copyWith(
                  color: AppColors.blackTint1,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                  decoration: BoxDecoration(
                    color: AppColors.lightGrey1,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppColors.blackTint2,
                      width: 1,
                    ),
                  ),
                  child: Text(
                    connoteValue,
                    style: textTheme.montserratTitleSmall.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.primaryBlue,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              SizedBox(
                width: 100,
                height: 40,
                child: AppButton(
                  text: 'Track',
                  color: AppColors.loginGreen,
                  onPressed: _onTrackingPressed,
                  height: 40,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          Expanded(
            child: Container(
              margin: const EdgeInsets.only(right: 6),
              child: AppButton(
                text: isLoading ? 'Loading...' : 'Receive all',
                color: _getReceiveAllButtonColor(),
                onPressed: isLoading ? () {} : _onReceiveAllPressed,
                height: 44,
              ),
            ),
          ),
          Expanded(
            child: Container(
              margin: const EdgeInsets.only(left: 6),
              child: AppButton(
                text: isLoading ? 'Loading...' : 'Not received',
                color: _getNotReceivedButtonColor(),
                onPressed: isLoading ? () {} : _onNotReceivedPressed,
                height: 44,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getReceiveAllButtonColor() {
    if (posReceived == true) {
      return AppColors.loginGreen; // Green when received
    }
    return AppColors.midGrey; // Grey when not received or no status
  }

  Color _getNotReceivedButtonColor() {
    if (posReceived == false) {
      return AppColors.loginRed; // Red when not received
    }
    return AppColors.midGrey; // Grey when received or no status
  }

  Widget _buildTableHeader(TextTheme textTheme) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
        boxShadow: [
          BoxShadow(
            color: AppColors.black10,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Container(
        height: 44,
        padding: const EdgeInsets.symmetric(horizontal: 20),
        decoration: const BoxDecoration(
          color: AppColors.lightGrey1,
          borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
          border: Border(
            bottom: BorderSide(
              color: AppColors.blackTint2,
              width: 1,
            ),
          ),
        ),
        child: Row(
          children: [
            // Item column
            Expanded(
              flex: 2,
              child: Text(
                'Item Name',
                style: textTheme.montserratTitleExtraSmall.copyWith(
                  color: AppColors.black,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            // Quantity column
            SizedBox(
              width: 80,
              child: Center(
                child: Text(
                  'Qty',
                  style: textTheme.montserratTitleExtraSmall.copyWith(
                    color: AppColors.black,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
            // Photo column
            SizedBox(
              width: 80,
              child: Center(
                child: Text(
                  'Photo',
                  style: textTheme.montserratTitleExtraSmall.copyWith(
                    color: AppColors.black,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPosItemsList() {
    if (posItems.isEmpty) {
      return const Padding(
        padding: EdgeInsets.all(0),
        child: EmptyState(message: 'No POS Items Found'),
      );
    }

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(12)),
        boxShadow: [
          BoxShadow(
            color: AppColors.black10,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: posItems.length,
        separatorBuilder: (context, index) => Container(
          margin: const EdgeInsets.symmetric(horizontal: 20),
          height: 1,
          color: AppColors.blackTint2,
        ),
        itemBuilder: (context, index) {
          final isLast = index == posItems.length - 1;
          return Container(
            decoration: BoxDecoration(
              borderRadius: isLast
                  ? const BorderRadius.vertical(bottom: Radius.circular(12))
                  : null,
            ),
            child: PosItemCard(
              posItem: posItems[index],
              onPhotoTap: () => _onPhotoTap(index),
            ),
          );
        },
      ),
    );
  }

  void _onPhotoTap(int index) {
    // Handle photo tap for specific item - similar to Android adapter's image click handling
    if (index >= 0 && index < posItems.length) {
      final posItem = posItems[index];

      if (posItem.photoUrl != null && posItem.photoUrl!.isNotEmpty) {
        // Navigate to web browser to view the image (similar to ImageFragment in Android)
        try {
          context.router.push(WebBrowserRoute(url: posItem.photoUrl!));
        } catch (e) {
          SnackBarService.error(
            context: context,
            message: 'Unable to open image viewer',
          );
        }
      } else {
        // Show message when no photo is available
        SnackBarService.info(
          context: context,
          message: 'No photo available for ${posItem.itemName ?? 'this item'}',
        );
      }
    } else {
      SnackBarService.error(
        context: context,
        message: 'Invalid item selected',
      );
    }
  }
}
