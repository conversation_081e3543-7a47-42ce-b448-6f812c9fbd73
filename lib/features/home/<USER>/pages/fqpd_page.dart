// FQPDPage - Dynamic Question Part Management
//
// This page handles two types of question part management:
//
// 1. RestrictedMultiQuestion (isMulti=true AND multiMeasurementId!=0):
//    - Queries database for saved counter values from other questions
//    - Displays exact number of template dropdown widgets based on saved count
//    - User can select items to populate each template dropdown
//    - User CANNOT add or delete the item count (count is fixed)
//    - Uses questionpartMultiId with format "a-b"
//    - User can change an item's questionpartId (change selection)
//
// 2. Non-restrictedMultiQuestion:
//    - Allows dynamic add/remove of question parts
//    - Each added item gets unique questionpartMultiId in "a-b" format
//    - User can add/remove items as needed
//    - User can change an item's questionpartId (change selection)
//
// Features:
// - Form data persistence with proper questionpartMultiId handling
// - Database integration using Realm
// - Navigation to QPMDPage with proper question part data
// - State management for dynamic UI updates

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/task_details/task_details_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/task_details/task_details_state.dart';

@RoutePage()
class FQPDPage extends StatefulWidget {
  final int questionId;
  final int taskId;
  final int formId;

  const FQPDPage({
    super.key,
    required this.questionId,
    required this.taskId,
    required this.formId,
  });

  @override
  State<FQPDPage> createState() => _FQPDPageState();
}

class _FQPDPageState extends State<FQPDPage> {
  // Current question data
  Question? _currentQuestion;

  // Available question parts from the current Question
  List<QuestionPart> get _availableQuestionParts =>
      _currentQuestion?.questionParts ?? [];

  // List of selected question parts for non-restrictedMultiQuestion
  final List<QuestionPart> _selectedQuestionParts = [];

  // List of template question parts for restrictedMultiQuestion
  final List<QuestionPart?> _templateQuestionParts = [];

  // Track if this is a restrictedMultiQuestion
  bool get _isRestrictedMultiQuestion {
    return _currentQuestion?.isMulti == true &&
        _currentQuestion?.multiMeasurementId != null &&
        _currentQuestion?.multiMeasurementId != 0;
  }

  // Track completion status for question parts
  final Map<String, bool> _completionStatus = {};

  // Map to store stable unique integer suffixes for each questionPart instance
  final Map<String, int> _questionPartUniqueIds = {};

  // Counter for generating unique integers
  int _nextUniqueId = 1;

  @override
  void initState() {
    super.initState();
    // Fetch task details when the page initializes
    context.read<TaskDetailsCubit>().getTaskDetail(widget.taskId);
  }

  void _initializeQuestionData(Question question) {
    _currentQuestion = question;
    _initializeQuestionParts();
    _loadSavedQuestionParts();
  }

  /// Initialize question parts based on question type
  void _initializeQuestionParts() {
    if (_isRestrictedMultiQuestion) {
      _initializeRestrictedMultiQuestion();
    }
    // For non-restrictedMultiQuestion, _selectedQuestionParts starts empty
  }

  /// Initialize restrictedMultiQuestion with saved value count
  void _initializeRestrictedMultiQuestion() {
    final savedValueCount = _getSavedValueCountForRestrictedMultiQuestion();
    debugPrint('RestrictedMultiQuestion saved value count: $savedValueCount');

    // Initialize template question parts list with the exact count
    // If no saved values, show at least one template dropdown
    final templateCount = savedValueCount > 0 ? savedValueCount : 1;
    _templateQuestionParts.clear();
    for (int i = 0; i < templateCount; i++) {
      _templateQuestionParts.add(null); // null means no selection yet
    }

    setState(() {});
  }

  /// Get saved value count for restrictedMultiQuestion from database
  /// This should look at counter values from OTHER questions (not the restrictedMultiQuestion itself)
  /// following the same pattern as question_page.dart
  int _getSavedValueCountForRestrictedMultiQuestion() {
    if (_currentQuestion?.questionId == null) {
      return 0;
    }

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm
          .query<TaskDetailModel>('taskId == \$0', [widget.taskId]).firstOrNull;

      if (taskModel == null) {
        debugPrint('Task not found in database for taskId: ${widget.taskId}');
        return 0;
      }

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == widget.formId)
          .firstOrNull;

      if (formModel == null) {
        debugPrint('Form not found in task for formId: ${widget.formId}');
        return 0;
      }

      // We need to get all questions from the form to find OTHER questions
      // Since we don't have direct access to the form entity here, we'll need to
      // look at all question answers and find the maximum counter value from
      // questions that are NOT this restrictedMultiQuestion

      int maxCount = 0;

      // Get all question answers for this form
      final allQuestionAnswers = formModel.questionAnswers.toList();

      // Group by questionId to find counter values from other questions
      final questionGroups = <int, List<QuestionAnswerModel>>{};
      for (final answer in allQuestionAnswers) {
        if (answer.questionId != null) {
          questionGroups.putIfAbsent(answer.questionId!, () => []).add(answer);
        }
      }

      // Check each question group (excluding the restrictedMultiQuestion itself)
      for (final entry in questionGroups.entries) {
        final questionId = entry.key;
        final answers = entry.value;

        // Skip the restrictedMultiQuestion itself
        if (questionId == _currentQuestion!.questionId!.toInt()) {
          continue;
        }

        // Look for counter values in this question's answers
        for (final answer in answers) {
          if (answer.measurementTextResult != null) {
            final counterValue = int.tryParse(answer.measurementTextResult!);
            if (counterValue != null && counterValue > maxCount) {
              maxCount = counterValue;
              debugPrint(
                  'Found counter value $counterValue in other question $questionId');
            }
          }
        }
      }

      debugPrint(
          'Found max counter value from OTHER questions for restrictedMultiQuestion: $maxCount');
      return maxCount;
    } catch (e) {
      debugPrint(
          'Error getting saved value count for restrictedMultiQuestion: $e');
      return 0;
    }
  }

  /// Load saved question parts from database
  void _loadSavedQuestionParts() {
    if (_currentQuestion?.questionId == null) {
      return;
    }

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm
          .query<TaskDetailModel>('taskId == \$0', [widget.taskId]).firstOrNull;

      if (taskModel == null) {
        debugPrint('Task not found in database for taskId: ${widget.taskId}');
        return;
      }

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == widget.formId)
          .firstOrNull;

      if (formModel == null) {
        debugPrint('Form not found in task for formId: ${widget.formId}');
        return;
      }

      // Find saved question answers for this question
      final savedAnswers = formModel.questionAnswers
          .where((answer) =>
              answer.questionId == _currentQuestion!.questionId!.toInt())
          .toList();

      if (savedAnswers.isEmpty) {
        debugPrint(
            'No saved answers found for question ${_currentQuestion!.questionId}');
        return;
      }

      // Process saved answers based on question type
      if (_isRestrictedMultiQuestion) {
        _loadRestrictedMultiQuestionData(savedAnswers);
      } else {
        _loadNonRestrictedMultiQuestionData(savedAnswers);
      }

      // Update completion status after loading data
      _updateCompletionStatus();

      setState(() {});
    } catch (e) {
      debugPrint('Error loading saved question parts: $e');
    }
  }

  /// Load data for restrictedMultiQuestion
  void _loadRestrictedMultiQuestionData(
      List<QuestionAnswerModel> savedAnswers) {
    for (int i = 0; i < _templateQuestionParts.length; i++) {
      logger(
          'Template question part at index $i: ${_templateQuestionParts[i]}');
    }
    // For restrictedMultiQuestion, load the selected question parts into template slots
    for (final answer in savedAnswers) {
      if (answer.questionpartId != null) {
        // Find the question part by ID
        final questionPart = _availableQuestionParts
            .where((qp) => qp.questionpartId == answer.questionpartId)
            .firstOrNull;

        if (questionPart != null) {
          // Find an empty template slot and assign this question part
          for (int i = 0; i < _templateQuestionParts.length; i++) {
            if (_templateQuestionParts[i] == null) {
              _templateQuestionParts[i] = questionPart;
              break;
            }
          }
        }
      }
    }
  }

  /// Load data for non-restrictedMultiQuestion
  void _loadNonRestrictedMultiQuestionData(
      List<QuestionAnswerModel> savedAnswers) {
    for (int i = 0; i < _templateQuestionParts.length; i++) {
      logger(
          'Template question part at index $i: ${_templateQuestionParts[i]}');
    }
    // For non-restrictedMultiQuestion, load selected question parts
    // We need to look at questionPartMultiId to handle "a-b" format correctly
    final processedMultiIds = <String>{};

    for (final answer in savedAnswers) {
      if (answer.questionpartId != null && answer.questionPartMultiId != null) {
        final multiId = answer.questionPartMultiId!;

        // Skip if we've already processed this multiId
        if (processedMultiIds.contains(multiId)) {
          continue;
        }
        processedMultiIds.add(multiId);

        // Find the question part by ID
        final questionPart = _availableQuestionParts
            .where((qp) => qp.questionpartId == answer.questionpartId)
            .firstOrNull;

        if (questionPart != null) {
          _selectedQuestionParts.add(questionPart);

          // Restore the unique ID mapping from the saved multiId
          if (multiId.contains('-')) {
            final parts = multiId.split('-');
            if (parts.length == 2) {
              final uniqueId = int.tryParse(parts[1]);
              if (uniqueId != null) {
                final instanceKey =
                    'selected_${questionPart.questionpartId}_${_selectedQuestionParts.length - 1}';
                _questionPartUniqueIds[instanceKey] = uniqueId;
                // Update the counter to ensure new IDs don't conflict
                if (uniqueId >= _nextUniqueId) {
                  _nextUniqueId = uniqueId + 1;
                }
              }
            }
          }
        }
      }
    }

    debugPrint(
        'Loaded ${_selectedQuestionParts.length} question parts for non-restrictedMultiQuestion');
  }

  /// Generate questionpartMultiId in "a-b" format with stable unique suffix
  String _generateQuestionPartMultiId(num? questionPartId,
      {String? instanceKey}) {
    final questionPartIdStr = '${questionPartId ?? 0}';

    // Create a unique key for this specific instance
    String keyToUse;
    if (instanceKey != null) {
      keyToUse = instanceKey;
    } else if (_isRestrictedMultiQuestion) {
      // For restricted mode, we need to determine which template position this is
      // This should be called with instanceKey, but fallback to questionPartId
      keyToUse = 'template_$questionPartIdStr';
    } else {
      // For non-restricted mode, we need to determine which selected item this is
      // This should be called with instanceKey, but fallback to questionPartId
      keyToUse = 'selected_$questionPartIdStr';
    }

    // Get or create a unique integer for this instance
    if (!_questionPartUniqueIds.containsKey(keyToUse)) {
      _questionPartUniqueIds[keyToUse] = _nextUniqueId++;
    }

    final uniqueSuffix = _questionPartUniqueIds[keyToUse]!;
    return '$questionPartIdStr-$uniqueSuffix';
  }

  /// Remove selected item for non-restrictedMultiQuestion
  void _removeSelectedItem(int index) {
    setState(() {
      _selectedQuestionParts.removeAt(index);
    });
    // Save the updated selection to database
    _saveQuestionPartSelections();
  }

  /// Check if a question part is completed by looking for measurement data
  bool _isQuestionPartCompleted(String questionPartMultiId) {
    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) {
        return false;
      }

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == widget.formId!.toInt())
          .firstOrNull;

      if (formModel == null) {
        return false;
      }

      // Find saved question answers for this question and questionPartMultiId
      final savedAnswers = formModel.questionAnswers
          .where((answer) =>
              answer.questionId == _currentQuestion!.questionId!.toInt() &&
              answer.questionPartMultiId == questionPartMultiId)
          .toList();

      // Check if there are answers with actual measurement data
      for (final answer in savedAnswers) {
        if (_hasActualMeasurementData(answer)) {
          return true;
        }
      }

      return false;
    } catch (e) {
      debugPrint('Error checking completion status: $e');
      return false;
    }
  }

  /// Check if a QuestionAnswerModel has actual measurement data (not just selection data)
  /// This method now validates that ALL measurements in the question part are properly filled
  bool _hasActualMeasurementData(QuestionAnswerModel answer) {
    // First check if this answer has basic measurement data
    if (answer.measurementId == null) return false;

    // Check if it has any measurement result data
    final hasTextResult = answer.measurementTextResult != null &&
        answer.measurementTextResult!.isNotEmpty;
    final hasOptionId = answer.measurementOptionId != null;
    final hasOptionIds = answer.measurementOptionIds != null &&
        answer.measurementOptionIds!.isNotEmpty;

    // If this specific answer doesn't have measurement data, return false
    if (!hasTextResult && !hasOptionId && !hasOptionIds) {
      return false;
    }

    // Now check if ALL measurements for this question part are completed
    return _areAllMeasurementsCompletedForQuestionPart(
        answer.questionPartMultiId);
  }

  /// Check if all measurements for a specific question part are completed
  /// This validates that every visible measurement in the question part has valid data
  bool _areAllMeasurementsCompletedForQuestionPart(
      String? questionPartMultiId) {
    if (questionPartMultiId == null || _currentQuestion?.measurements == null) {
      return false;
    }

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm
          .query<TaskDetailModel>('taskId == \$0', [widget.taskId]).firstOrNull;

      if (taskModel == null) return false;

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == widget.formId)
          .firstOrNull;

      if (formModel == null) return false;

      // Get all saved answers for this question part
      final savedAnswers = formModel.questionAnswers
          .where((answer) =>
              answer.questionId == _currentQuestion!.questionId!.toInt() &&
              answer.questionPartMultiId == questionPartMultiId &&
              answer.measurementId != null)
          .toList();

      // Get all measurements for this question
      final measurements = _currentQuestion!.measurements!;

      // Count how many measurements should be completed
      // We need to simulate the visibility logic from QPMDPage
      int requiredMeasurements = 0;
      int completedMeasurements = 0;

      for (final measurement in measurements) {
        if (measurement.measurementId == null) continue;

        // For now, assume all measurements are visible
        // In a more complete implementation, we would need to replicate
        // the conditional logic from QPMDPage to determine visibility
        requiredMeasurements++;

        // Check if this measurement has a completed answer
        final measurementAnswer = savedAnswers
            .where((answer) =>
                answer.measurementId == measurement.measurementId!.toInt())
            .firstOrNull;

        if (measurementAnswer != null &&
            _isAnswerComplete(measurementAnswer, measurement)) {
          completedMeasurements++;
        }
      }

      // All required measurements must be completed
      return requiredMeasurements > 0 &&
          completedMeasurements == requiredMeasurements;
    } catch (e) {
      debugPrint('Error checking all measurements completion: $e');
      return false;
    }
  }

  /// Check if a specific answer is complete based on measurement type and validation
  bool _isAnswerComplete(QuestionAnswerModel answer, Measurement measurement) {
    // Check if the answer has measurement data
    final hasTextResult = answer.measurementTextResult != null &&
        answer.measurementTextResult!.isNotEmpty;
    final hasOptionId = answer.measurementOptionId != null;
    final hasOptionIds = answer.measurementOptionIds != null &&
        answer.measurementOptionIds!.isNotEmpty;

    if (!hasTextResult && !hasOptionId && !hasOptionIds) {
      return false;
    }

    // Check if the value would be considered empty based on measurement type
    dynamic value;
    switch (measurement.measurementTypeId) {
      case 1: // Text field
      case 2: // Text field
        value = answer.measurementTextResult;
        return value != null && value.toString().trim().isNotEmpty;
      case 3: // Checkbox
        value = answer.measurementOptionId;
        return value != null; // Checkbox should have an option selected
      case 4: // Dropdown
      case 5: // Dropdown
      case 9: // Date picker
        value = answer.measurementOptionId;
        return value != null;
      case 6: // Multi-select
        value = answer.measurementOptionIds;
        return value != null && value.isNotEmpty;
      case 7: // Counter
        value = answer.measurementTextResult;
        if (value == null) return false;
        final numValue = int.tryParse(value.toString());
        return numValue != null && numValue > 0;
      default:
        return hasTextResult || hasOptionId || hasOptionIds;
    }
  }

  /// Update completion status for all question parts
  void _updateCompletionStatus() {
    if (_isRestrictedMultiQuestion) {
      // For restricted multi-question, check template question parts
      for (int i = 0; i < _templateQuestionParts.length; i++) {
        final questionPart = _templateQuestionParts[i];
        if (questionPart != null) {
          final instanceKey = 'template_${questionPart.questionpartId}_$i';
          final questionPartMultiId = _generateQuestionPartMultiId(
              questionPart.questionpartId,
              instanceKey: instanceKey);
          _completionStatus[questionPartMultiId] =
              _isQuestionPartCompleted(questionPartMultiId);
        }
      }
    } else {
      // For non-restricted multi-question, check selected question parts
      for (int i = 0; i < _selectedQuestionParts.length; i++) {
        final questionPart = _selectedQuestionParts[i];
        final instanceKey = 'selected_${questionPart.questionpartId}_$i';
        final questionPartMultiId = _generateQuestionPartMultiId(
            questionPart.questionpartId,
            instanceKey: instanceKey);
        _completionStatus[questionPartMultiId] =
            _isQuestionPartCompleted(questionPartMultiId);
      }
    }
  }

  /// Save question part selections to database
  Future<void> _saveQuestionPartSelections() async {
    if (_currentQuestion?.questionId == null) {
      debugPrint('Cannot save: missing questionId');
      return;
    }

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) {
        debugPrint('Task not found in database for taskId: ${widget.taskId}');
        return;
      }

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == widget.formId!.toInt())
          .firstOrNull;

      if (formModel == null) {
        debugPrint('Form not found in task for formId: ${widget.formId}');
        return;
      }

      realm.write(() {
        // Remove existing selection records for this question (preserve measurement data)
        // Selection records have questionpartId but no measurementId
        // Measurement data records have both questionpartId and measurementId
        final existingSelectionAnswers = formModel.questionAnswers
            .where((qa) =>
                qa.questionId == _currentQuestion!.questionId!.toInt() &&
                qa.measurementId ==
                    null) // Only remove selection records, not measurement data
            .toList();

        for (final existingAnswer in existingSelectionAnswers) {
          logger('Removing selection record: ${existingAnswer.toEJson()}');
          formModel.questionAnswers.remove(existingAnswer);
        }

        debugPrint(
            'Removed ${existingSelectionAnswers.length} selection records, preserved measurement data');

        // Add new question answers for selected question parts
        if (_isRestrictedMultiQuestion) {
          // For restrictedMultiQuestion, save template selections
          for (int i = 0; i < _templateQuestionParts.length; i++) {
            final questionPart = _templateQuestionParts[i];
            if (questionPart != null) {
              final instanceKey = 'template_${questionPart.questionpartId}_$i';
              final questionAnswer = QuestionAnswerModel(
                taskId: widget.taskId,
                formId: widget.formId,
                questionId: _currentQuestion!.questionId!.toInt(),
                questionpartId: questionPart.questionpartId?.toInt(),
                flip: _currentQuestion?.flip,
                questionPartMultiId: _generateQuestionPartMultiId(
                    questionPart.questionpartId,
                    instanceKey: instanceKey),
                isComment: false,
              );
              formModel.questionAnswers.add(questionAnswer);
            }
          }
        } else {
          // For non-restrictedMultiQuestion, save selected question parts
          for (int i = 0; i < _selectedQuestionParts.length; i++) {
            final questionPart = _selectedQuestionParts[i];
            final instanceKey = 'selected_${questionPart.questionpartId}_$i';
            final questionAnswer = QuestionAnswerModel(
              taskId: widget.taskId,
              formId: widget.formId,
              questionId: _currentQuestion!.questionId!.toInt(),
              questionpartId: questionPart.questionpartId?.toInt(),
              flip: _currentQuestion?.flip,
              questionPartMultiId: _generateQuestionPartMultiId(
                  questionPart.questionpartId,
                  instanceKey: instanceKey),
              isComment: false,
            );
            formModel.questionAnswers.add(questionAnswer);
          }
        }
      });

      debugPrint('Successfully saved question part selections to database');
    } catch (e) {
      debugPrint('Error saving question part selections: $e');
    }
  }

  /// Show template item selection bottom sheet for restrictedMultiQuestion
  void _showTemplateItemBottomSheet(int templateIndex) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useRootNavigator: true,
      builder: (context) {
        // Calculate dynamic height based on content
        const itemHeight = 56.0;
        const headerHeight = 80.0;
        const bottomPadding = 16.0;
        final maxContentHeight = MediaQuery.of(context).size.height * 0.7;
        final calculatedHeight = headerHeight +
            (_availableQuestionParts.length * itemHeight) +
            bottomPadding;
        final finalHeight = calculatedHeight > maxContentHeight
            ? maxContentHeight
            : calculatedHeight;

        return Container(
          height: finalHeight,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12),
                decoration: BoxDecoration(
                  color: AppColors.blackTint2,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Select item',
                  style: Theme.of(context).textTheme.montserratheadingmedium,
                ),
              ),

              // Items list
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  itemCount: _availableQuestionParts.length,
                  itemBuilder: (context, index) {
                    final questionPart = _availableQuestionParts[index];
                    final currentSelection =
                        _templateQuestionParts[templateIndex];
                    final isCurrentSelection =
                        currentSelection?.questionpartId ==
                            questionPart.questionpartId;

                    return InkWell(
                      onTap: () {
                        setState(() {
                          _templateQuestionParts[templateIndex] = questionPart;
                        });
                        Navigator.pop(context);
                        // Save the updated selection to database
                        _saveQuestionPartSelections();
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12.0),
                        decoration: const BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: AppColors.blackTint2,
                              width: 0.5,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                questionPart.questionpartDescription ??
                                    'Unnamed Item',
                                style: Theme.of(context)
                                    .textTheme
                                    .montserratFormsField
                                    .copyWith(
                                      color: AppColors.black,
                                    ),
                              ),
                            ),
                            if (isCurrentSelection)
                              const Icon(
                                Icons.radio_button_checked,
                                color: AppColors.primaryBlue,
                                size: 20,
                              ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: CustomAppBar(
        title: _currentQuestion?.questionDescription ?? 'Store details',
      ),
      body: BlocBuilder<TaskDetailsCubit, TaskDetailsState>(
        builder: (context, state) {
          if (state is TaskDetailsLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is TaskDetailsError) {
            return Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Error loading task details: ${state.message}',
                  style: textTheme.bodyLarge,
                ),
              ),
            );
          }

          if (state is! TaskDetailsSuccess) {
            return const Center(child: CircularProgressIndicator());
          }

          final task = state.taskDetail;
          final form = task.forms?.firstWhere(
            (f) => f.formId == widget.formId,
            orElse: () => entities.Form(),
          );

          final question = form?.questions?.firstWhere(
            (q) => q.questionId == widget.questionId,
            orElse: () => entities.Question(),
          );

          if (question?.questionId == null) {
            return const Center(
              child: Text('Question not found'),
            );
          }

          // Initialize question data if not already done
          if (_currentQuestion == null) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _initializeQuestionData(question!);
            });
            return const Center(child: CircularProgressIndicator());
          }

          // Update completion status when returning to this page
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _updateCompletionStatus();
            setState(
                () {}); // Trigger rebuild to show updated completion status
          });

          return _availableQuestionParts.isEmpty
              ? _buildEmptyState(textTheme)
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (_isRestrictedMultiQuestion) ...[
                        // RestrictedMultiQuestion: Show template dropdowns
                        ..._buildRestrictedMultiQuestionWidgets(textTheme),
                      ] else ...[
                        // Non-restrictedMultiQuestion: Show add dropdown and selected items
                        _buildMainDropdown(textTheme),
                        const Gap(16),

                        // Selected items list
                        if (_selectedQuestionParts.isNotEmpty) ...[
                          ..._selectedQuestionParts
                              .asMap()
                              .entries
                              .map((entry) {
                            final index = entry.key;
                            final questionPart = entry.value;
                            return Padding(
                              padding: const EdgeInsets.only(bottom: 16),
                              child: _buildSelectedItemCard(
                                  questionPart, index, textTheme),
                            );
                          }),
                        ],
                      ],
                    ],
                  ),
                );
        },
      ),
    );
  }

  /// Build widgets for restrictedMultiQuestion
  List<Widget> _buildRestrictedMultiQuestionWidgets(TextTheme textTheme) {
    final widgets = <Widget>[];

    for (int i = 0; i < _templateQuestionParts.length; i++) {
      widgets.add(
        Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: _buildTemplateDropdown(i, textTheme),
        ),
      );
    }

    return widgets;
  }

  /// Build a template dropdown for restrictedMultiQuestion
  Widget _buildTemplateDropdown(int index, TextTheme textTheme) {
    final selectedQuestionPart = _templateQuestionParts[index];

    // Get completion status for this question part
    final questionPartMultiId = selectedQuestionPart != null
        ? _generateQuestionPartMultiId(selectedQuestionPart.questionpartId,
            instanceKey:
                'template_${selectedQuestionPart.questionpartId}_$index')
        : '';
    final isCompleted = _completionStatus[questionPartMultiId] ?? false;

    return Row(
      children: [
        // Dropdown container
        Expanded(
          child: GestureDetector(
            onTap: selectedQuestionPart != null
                ? () {
                    // Navigate to QPMDPage with the selected question part
                    final questionPartForNavigation = QuestionPart(
                      projectid: selectedQuestionPart.projectid,
                      questionpartId: selectedQuestionPart.questionpartId,
                      questionpartDescription:
                          selectedQuestionPart.questionpartDescription,
                      price: selectedQuestionPart.price,
                      modifiedTimeStampQuestionpart:
                          selectedQuestionPart.modifiedTimeStampQuestionpart,
                      targetByCycle: selectedQuestionPart.targetByCycle,
                      targetByGroup: selectedQuestionPart.targetByGroup,
                      targetByCompany: selectedQuestionPart.targetByCompany,
                      targetByRegion: selectedQuestionPart.targetByRegion,
                      targetByBudget: selectedQuestionPart.targetByBudget,
                      osaForm: selectedQuestionPart.osaForm,
                      companyId: selectedQuestionPart.companyId,
                      itemImage: selectedQuestionPart.itemImage,
                      targeted: selectedQuestionPart.targeted,
                      questionpartMultiId: _generateQuestionPartMultiId(
                          selectedQuestionPart.questionpartId,
                          instanceKey:
                              'template_${selectedQuestionPart.questionpartId}_$index'),
                    );

                    context.router.push(QPMDRoute(
                      question: _currentQuestion,
                      questionPart: questionPartForNavigation,
                      taskId: widget.taskId,
                      formId: widget.formId,
                    ));
                  }
                : null,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.black10,
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Row(
                children: [
                  // Dropdown content
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Progress indicator for template dropdowns
                        Text(
                          '${index + 1}/${_templateQuestionParts.length}',
                          style: textTheme.montserratParagraphXsmall.copyWith(
                            color: AppColors.blackTint1,
                          ),
                        ),
                        const Gap(4),
                        Text(
                          selectedQuestionPart?.questionpartDescription ??
                              'Please select...',
                          style: textTheme.montserratFormsField.copyWith(
                            color: selectedQuestionPart != null
                                ? AppColors.black
                                : AppColors.blackTint1,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Dropdown arrow
                  GestureDetector(
                    onTap: () => _showTemplateItemBottomSheet(index),
                    child: Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: AppColors.loginGreen,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Icon(
                        Icons.keyboard_arrow_down,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        // Completion status indicator positioned outside the dropdown (only show if item is selected)
        // if (selectedQuestionPart != null) ...[
        const Gap(12),
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: isCompleted ? AppColors.primaryBlue : Colors.grey,
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.check,
            color: Colors.white,
            size: 14,
          ),
        ),
        // ],
      ],
    );
  }

  Widget _buildEmptyState(TextTheme textTheme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.inventory_2_outlined,
              size: 64,
              color: AppColors.blackTint1,
            ),
            const Gap(16),
            Text(
              'No Question Parts Available',
              style: textTheme.montserratheadingmedium.copyWith(
                color: AppColors.blackTint1,
              ),
            ),
            const Gap(8),
            Text(
              'This question does not have any question parts to display.',
              style: textTheme.montserratParagraphSmall.copyWith(
                color: AppColors.blackTint1,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMainDropdown(TextTheme textTheme) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: AppColors.black10,
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        children: [
          // Add button (+)
          GestureDetector(
            onTap: _showAddItemBottomSheet,
            child: Container(
              width: 32,
              height: 32,
              decoration: const BoxDecoration(
                color: AppColors.loginGreen,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.add,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
          const Gap(12),

          // Dropdown content
          Expanded(
            child: Text(
              'Please select...',
              style: textTheme.montserratFormsField.copyWith(
                color: AppColors.blackTint1,
              ),
            ),
          ),

          // Dropdown arrow
          GestureDetector(
            onTap: _showAddItemBottomSheet,
            child: Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: AppColors.loginGreen,
                borderRadius: BorderRadius.circular(10),
              ),
              child: const Icon(
                Icons.keyboard_arrow_down,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedItemCard(
      QuestionPart questionPart, int index, TextTheme textTheme) {
    final hasImage = questionPart.itemImage != null &&
        questionPart.itemImage != '0' &&
        questionPart.itemImage!.isNotEmpty;

    // Get completion status for this question part
    final instanceKey = 'selected_${questionPart.questionpartId}_$index';
    final questionPartMultiId = _generateQuestionPartMultiId(
        questionPart.questionpartId,
        instanceKey: instanceKey);
    final isCompleted = _completionStatus[questionPartMultiId] ?? false;

    return Row(
      children: [
        // const Gap(4),
        // GestureDetector(
        //   onTap: () => _removeSelectedItem(index),
        //   behavior: HitTestBehavior.opaque,
        //   child: Container(
        //     width: 24,
        //     height: 24,
        //     decoration: const BoxDecoration(
        //       color: Colors.red,
        //       shape: BoxShape.circle,
        //     ),
        //     child: const Icon(
        //       Icons.remove,
        //       color: Colors.white,
        //       size: 14,
        //     ),
        //   ),
        // ),
        // const Gap(8),
        // Card container
        Expanded(
          child: GestureDetector(
            onTap: () {
              // Create a copy with proper questionpartMultiId for navigation
              final questionPartForNavigation = QuestionPart(
                projectid: questionPart.projectid,
                questionpartId: questionPart.questionpartId,
                questionpartDescription: questionPart.questionpartDescription,
                price: questionPart.price,
                modifiedTimeStampQuestionpart:
                    questionPart.modifiedTimeStampQuestionpart,
                targetByCycle: questionPart.targetByCycle,
                targetByGroup: questionPart.targetByGroup,
                targetByCompany: questionPart.targetByCompany,
                targetByRegion: questionPart.targetByRegion,
                targetByBudget: questionPart.targetByBudget,
                osaForm: questionPart.osaForm,
                companyId: questionPart.companyId,
                itemImage: questionPart.itemImage,
                targeted: questionPart.targeted,
                questionpartMultiId: _generateQuestionPartMultiId(
                    questionPart.questionpartId,
                    instanceKey: instanceKey),
              );

              // Navigate to QPMDPage with the current question
              context.router.push(QPMDRoute(
                question: _currentQuestion,
                questionPart: questionPartForNavigation,
                taskId: widget.taskId,
                formId: widget.formId,
              ));
            },
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.black10,
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Row(
                children: [
                  // Remove button (-)
                  GestureDetector(
                    onTap: () => _removeSelectedItem(index),
                    behavior: HitTestBehavior.opaque,
                    child: Container(
                      width: 32,
                      height: 32,
                      decoration: const BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.remove,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
                  const Gap(12),

                  // Content area
                  Expanded(
                    child: Row(
                      children: [
                        // Text content
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Progress indicator (like in the image)
                              Text(
                                '${index + 1}/${_selectedQuestionParts.length}',
                                style: textTheme.montserratParagraphXsmall
                                    .copyWith(
                                  color: AppColors.blackTint1,
                                ),
                              ),
                              const Gap(4),
                              Text(
                                questionPart.questionpartDescription ??
                                    'Unnamed Item',
                                style: textTheme.montserratFormsField,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),

                        // Image thumbnail (if available)
                        if (hasImage) ...[
                          const Gap(12),
                          Container(
                            width: 60,
                            height: 60,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              color: AppColors.lightGrey1,
                              border: Border.all(color: AppColors.blackTint2),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(10),
                              child: Image.network(
                                questionPart.itemImage!,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    color: AppColors.lightGrey1,
                                    child: const Icon(
                                      Icons.image,
                                      color: AppColors.blackTint1,
                                      size: 24,
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  const Gap(12),

                  // Change selection dropdown arrow
                  GestureDetector(
                    onTap: () => _showChangeItemBottomSheet(index),
                    behavior: HitTestBehavior.opaque,
                    child: Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: AppColors.loginGreen,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Icon(
                        Icons.keyboard_arrow_down,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        // Completion status indicator positioned outside the card
        const Gap(12),
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: isCompleted ? AppColors.primaryBlue : Colors.grey,
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.check,
            color: Colors.white,
            size: 14,
          ),
        ),
      ],
    );
  }

  void _showAddItemBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useRootNavigator: true,
      builder: (context) {
        // Calculate dynamic height based on content
        const itemHeight = 56.0;
        const headerHeight = 80.0;
        const bottomPadding = 16.0;
        final maxContentHeight = MediaQuery.of(context).size.height * 0.7;
        final calculatedHeight = headerHeight +
            (_availableQuestionParts.length * itemHeight) +
            bottomPadding;
        final finalHeight = calculatedHeight > maxContentHeight
            ? maxContentHeight
            : calculatedHeight;

        return Container(
          height: finalHeight,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12),
                decoration: BoxDecoration(
                  color: AppColors.blackTint2,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Select item',
                  style: Theme.of(context).textTheme.montserratheadingmedium,
                ),
              ),

              // Items list
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  itemCount: _availableQuestionParts.length,
                  itemBuilder: (context, index) {
                    final questionPart = _availableQuestionParts[index];

                    return InkWell(
                      onTap: () {
                        setState(() {
                          _selectedQuestionParts.add(questionPart);
                        });
                        Navigator.pop(context);
                        // Save the updated selection to database
                        _saveQuestionPartSelections();
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12.0),
                        decoration: const BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: AppColors.blackTint2,
                              width: 0.5,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                questionPart.questionpartDescription ??
                                    'Unnamed Item',
                                style: Theme.of(context)
                                    .textTheme
                                    .montserratFormsField
                                    .copyWith(
                                      color: AppColors.black,
                                    ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showChangeItemBottomSheet(int index) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useRootNavigator: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        // Calculate dynamic height based on content
        const itemHeight = 56.0;
        const headerHeight = 80.0;
        const bottomPadding = 16.0;
        final maxContentHeight = MediaQuery.of(context).size.height * 0.7;
        final calculatedHeight = headerHeight +
            (_availableQuestionParts.length * itemHeight) +
            bottomPadding;
        final finalHeight = calculatedHeight > maxContentHeight
            ? maxContentHeight
            : calculatedHeight;

        return Container(
          height: finalHeight,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12),
                decoration: BoxDecoration(
                  color: AppColors.blackTint2,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Change Selection',
                  style: Theme.of(context).textTheme.montserratheadingmedium,
                ),
              ),

              // Items list
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  itemCount: _availableQuestionParts.length,
                  itemBuilder: (context, itemIndex) {
                    final questionPart = _availableQuestionParts[itemIndex];
                    final currentItem = _selectedQuestionParts[index];
                    final isCurrentSelection = currentItem.questionpartId ==
                        questionPart.questionpartId;
                    final isAlreadySelected = _selectedQuestionParts.any(
                        (item) =>
                            item.questionpartId == questionPart.questionpartId);

                    return InkWell(
                      onTap: isCurrentSelection
                          ? null
                          : () {
                              setState(() {
                                _selectedQuestionParts[index] = questionPart;
                              });
                              Navigator.pop(context);
                              // Save the updated selection to database
                              _saveQuestionPartSelections();
                            },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12.0),
                        decoration: const BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: AppColors.blackTint2,
                              width: 0.5,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                questionPart.questionpartDescription ??
                                    'Unnamed Item',
                                style: Theme.of(context)
                                    .textTheme
                                    .montserratFormsField
                                    .copyWith(
                                      color: (isAlreadySelected &&
                                              !isCurrentSelection)
                                          ? AppColors.blackTint1
                                          : AppColors.black,
                                    ),
                              ),
                            ),
                            if (isCurrentSelection)
                              const Icon(
                                Icons.radio_button_checked,
                                color: AppColors.primaryBlue,
                                size: 20,
                              )
                            else if (isAlreadySelected)
                              const Icon(
                                Icons.check,
                                color: AppColors.loginGreen,
                                size: 20,
                              ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
