import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/widgets/form_card.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';

@RoutePage()
class FormPage extends StatefulWidget {
  final entities.TaskDetail task;

  const FormPage({
    super.key,
    required this.task,
  });

  @override
  State<FormPage> createState() => _FormPageState();
}

class _FormPageState extends State<FormPage> with RouteAware {
  // Get forms from the task
  List<entities.Form>? get formItems => widget.task.forms;

  // Cache for progress data to avoid recalculating on every build
  final Map<int, Map<String, dynamic>> _progressCache = {};

  @override
  void initState() {
    super.initState();
    // Initial load of progress data
    _refreshAllProgress();
  }

  @override
  void didPopNext() {
    super.didPopNext();
    // Refresh progress when returning from child pages
    _refreshAllProgress();
  }

  /// Refresh progress data for all forms by clearing cache and triggering rebuild
  void _refreshAllProgress() {
    _progressCache.clear();
    if (mounted) {
      setState(() {});
    }
  }

  /// Calculate dynamic progress for a specific form based on database data
  Map<String, dynamic> _calculateFormProgress(entities.Form form) {
    final formId = form.formId?.toInt();
    if (formId == null) {
      return {'progress': 0.0, 'progressText': '0 of 0'};
    }

    // Check cache first
    if (_progressCache.containsKey(formId)) {
      return _progressCache[formId]!;
    }

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.task.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) {
        final result = _getFallbackProgress(form);
        _progressCache[formId] = result;
        return result;
      }

      // Find the form with the matching formId
      final formModel =
          taskModel.forms.where((f) => f.formId == formId).firstOrNull;

      if (formModel == null) {
        final result = _getFallbackProgress(form);
        _progressCache[formId] = result;
        return result;
      }

      // Use kTotal and kCompleted from database if available
      final kTotal = formModel.kTotal;
      final kCompleted = formModel.kCompleted;

      if (kTotal != null && kCompleted != null) {
        // Use simplified progress tracking from database
        final progress = kTotal > 0 ? kCompleted / kTotal : 0.0;
        final progressText = '$kCompleted of $kTotal';

        final result = {'progress': progress, 'progressText': progressText};
        _progressCache[formId] = result;
        return result;
      } else {
        // Fallback to question count if kTotal/kCompleted not available
        final result = _getFallbackProgress(form);
        _progressCache[formId] = result;
        return result;
      }
    } catch (e) {
      final result = _getFallbackProgress(form);
      _progressCache[formId] = result;
      return result;
    }
  }

  /// Get fallback progress based on total question count
  Map<String, dynamic> _getFallbackProgress(entities.Form form) {
    final questions = form.questions ?? [];
    final totalQuestions = questions.length;
    return {'progress': 0.0, 'progressText': '0 of $totalQuestions'};
  }

  /// Check if a question is completed based on saved answers in the database
  bool _isQuestionCompleted(entities.Question question, FormModel formModel) {
    final questionId = question.questionId?.toInt();
    if (questionId == null) return false;

    // Get all saved answers for this question
    final savedAnswers = formModel.questionAnswers
        .where((answer) => answer.questionId == questionId)
        .toList();

    if (savedAnswers.isEmpty) return false;

    // For multi-questions, check if there are any completed instances with actual measurement data
    if (question.isMulti == true) {
      return savedAnswers.any((answer) => _hasActualMeasurementData(answer));
    }

    // For regular questions, check if all required measurements have valid answers
    final measurements = question.measurements ?? [];

    if (measurements.isEmpty) {
      // If no measurements, just check if there are any saved answers
      return savedAnswers.isNotEmpty;
    }

    // Check if all required measurements are completed
    int requiredMeasurements = 0;
    int completedMeasurements = 0;

    for (final measurement in measurements) {
      if (measurement.measurementId == null) continue;

      // Check if this measurement is required
      final isRequired = _isMeasurementRequired(measurement);
      if (isRequired) {
        requiredMeasurements++;

        // Check if this measurement has a valid answer
        final hasValidAnswer = savedAnswers.any((answer) =>
            answer.measurementId == measurement.measurementId?.toInt() &&
            _hasActualMeasurementData(answer));

        if (hasValidAnswer) {
          completedMeasurements++;
        }
      }
    }

    // If no required measurements, check if any measurement has data
    if (requiredMeasurements == 0) {
      return savedAnswers.any((answer) => _hasActualMeasurementData(answer));
    }

    // All required measurements must be completed
    return completedMeasurements == requiredMeasurements;
  }

  /// Check if a measurement is required based on measurement_validations array
  bool _isMeasurementRequired(entities.Measurement measurement) {
    final validations = measurement.measurementValidations ?? [];

    for (final validation in validations) {
      if (validation.required == true) {
        return true;
      }
    }
    return false;
  }

  /// Check if a QuestionAnswer has actual measurement data (not just selection data)
  bool _hasActualMeasurementData(QuestionAnswerModel answer) {
    // Check if the answer has actual measurement data
    if (answer.measurementId == null) {
      return false; // No measurement ID means it's just a selection entry
    }

    // Check if it has any measurement result data
    final hasTextResult = answer.measurementTextResult != null &&
        answer.measurementTextResult!.isNotEmpty;
    final hasOptionId = answer.measurementOptionId != null;
    final hasOptionIds = answer.measurementOptionIds != null &&
        answer.measurementOptionIds!.isNotEmpty;

    return hasTextResult || hasOptionId || hasOptionIds;
  }

  /// Get visible questions based on conditional logic (similar to QuestionPage.visibleQuestions)
  List<entities.Question> _getVisibleQuestions(
      entities.Form form, FormModel formModel) {
    final questions = form.questions ?? [];
    if (questions.isEmpty) return [];

    // Initialize question visibility map
    final questionVisibility = <num, bool>{};

    // Step 1: Initialize all questions as visible by default
    for (final question in questions) {
      if (question.questionId != null) {
        questionVisibility[question.questionId!] = true;
      }
    }

    // Step 2: Apply conditional logic based on saved answers
    _processQuestionConditionalLogic(questions, formModel, questionVisibility);

    // Step 3: Apply restricted multi-question visibility logic
    _handleRestrictedMultiQuestionVisibility(
        questions, formModel, questionVisibility);

    // Step 4: Filter questions based on visibility
    final visibleQuestions = questions.where((question) {
      // Always show questions without questionId (like comment items)
      if (question.questionId == null) return true;
      // Show questions based on visibility state (default to true if not set)
      final isVisible = questionVisibility[question.questionId!] ?? true;
      return isVisible;
    }).toList();

    return visibleQuestions;
  }

  /// Process question conditional logic based on saved answers (similar to QuestionPage._processQuestionConditionalLogic)
  void _processQuestionConditionalLogic(List<entities.Question> questions,
      FormModel formModel, Map<num, bool> questionVisibility) {
    try {
      for (final question in questions) {
        if (question.questionId == null ||
            question.questionConditions == null) {
          continue;
        }

        final questionId = question.questionId!;

        // Get saved answers for this question
        final savedAnswers = formModel.questionAnswers
            .where((answer) => answer.questionId == questionId.toInt())
            .toList();

        // Process each condition for this question
        for (final condition in question.questionConditions!) {
          if (condition.measurementOptionId == null ||
              condition.actionQuestionId == null) {
            continue;
          }

          // Check if the condition is met based on saved answers
          final conditionMet = savedAnswers.any((answer) =>
              answer.measurementOptionId ==
              condition.measurementOptionId!.toInt());

          if (conditionMet && condition.action != null) {
            _applyQuestionConditionalAction(condition.actionQuestionId!,
                condition.action!, questionVisibility);
          }
        }
      }
    } catch (e) {
      // Error processing question conditional logic - silently continue
    }
  }

  /// Apply conditional action (show/hide) to a target question
  void _applyQuestionConditionalAction(
      num targetQuestionId, String action, Map<num, bool> questionVisibility) {
    if (action.toLowerCase() == 'appear') {
      questionVisibility[targetQuestionId] = true;
    } else if (action.toLowerCase() == 'disappear') {
      questionVisibility[targetQuestionId] = false;
    }
  }

  /// Apply restricted multi-question visibility logic (similar to QuestionPage._handleRestrictedMultiQuestionVisibility)
  void _handleRestrictedMultiQuestionVisibility(
      List<entities.Question> questions,
      FormModel formModel,
      Map<num, bool> questionVisibility) {
    try {
      // Find the restricted multi-question (isMulti == true && multiMeasurementId != null && multiMeasurementId != 0)
      entities.Question? restrictedMultiQuestion;
      for (final question in questions) {
        if (question.isMulti == true &&
            question.multiMeasurementId != null &&
            question.multiMeasurementId != 0) {
          restrictedMultiQuestion = question;
          break;
        }
      }

      if (restrictedMultiQuestion?.questionId == null) return;

      // Check if any OTHER question (not the restrictedMultiQuestion) has saved counter value > 0
      final hasOtherQuestionWithValue = _hasOtherQuestionWithCounterValue(
          questions, restrictedMultiQuestion!, formModel);

      if (hasOtherQuestionWithValue) {
        // Make ALL questions visible when other questions have value > 0
        for (final question in questions) {
          if (question.questionId != null) {
            questionVisibility[question.questionId!] = true;
          }
        }
      } else {
        // Initially: Hide the restrictedMultiQuestion, show all other questions
        for (final question in questions) {
          if (question.questionId != null) {
            if (question.questionId == restrictedMultiQuestion.questionId) {
              // Hide the restrictedMultiQuestion
              questionVisibility[question.questionId!] = false;
            } else {
              // Show all other questions
              questionVisibility[question.questionId!] = true;
            }
          }
        }
      }
    } catch (e) {
      // Error in restricted multi-question visibility logic - silently continue
    }
  }

  /// Check if any OTHER question (not the restrictedMultiQuestion) has saved counter value > 0
  bool _hasOtherQuestionWithCounterValue(List<entities.Question> questions,
      entities.Question restrictedMultiQuestion, FormModel formModel) {
    for (final question in questions) {
      if (question.questionId == null ||
          question.questionId == restrictedMultiQuestion.questionId) {
        continue;
      }

      // Get saved answers for this question
      final savedAnswers = formModel.questionAnswers
          .where((answer) => answer.questionId == question.questionId!.toInt())
          .toList();

      // Check if any answer has a counter value > 0 (stored in measurementTextResult)
      for (final answer in savedAnswers) {
        if (answer.measurementTextResult != null) {
          final counterValue = int.tryParse(answer.measurementTextResult!);
          if (counterValue != null && counterValue > 0) {
            return true;
          }
        }
      }
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: const CustomAppBar(
        title: 'Forms',
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Gap(8),
            formItems == null || formItems!.isEmpty
                ? Center(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        'No forms available for this task',
                        style: textTheme.bodyLarge,
                      ),
                    ),
                  )
                : ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 8.0, vertical: 8.0),
                    itemCount: formItems!.length,
                    itemBuilder: (context, index) {
                      final form = formItems![index];

                      // Calculate dynamic progress based on database data
                      final progressData = _calculateFormProgress(form);
                      final progress = progressData['progress'] as double;
                      final progressText =
                          progressData['progressText'] as String;

                      bool isMandatory = form.isMandatory ?? false;

                      return GestureDetector(
                        onTap: () async {
                          if (form.isVisionForm == true) {
                            await context.router.push(
                                WebBrowserRoute(url: form.visionFormUrl ?? ''));
                          } else {
                            // Navigate to QuestionPage and refresh progress on return
                            await context.router.push(QuestionRoute(
                              form: form,
                              taskId: widget.task.taskId,
                            ));
                          }
                          // Refresh progress after returning from navigation
                          _refreshAllProgress();
                        },
                        child: FormCard(
                          title: form.formName ?? 'Unnamed Form',
                          progress: progress,
                          progressText: progressText,
                          width: 1.0, // Full width for list view
                          isMandatory: isMandatory,
                          isVisionForm: form.isVisionForm ?? false,
                        ),
                      );
                    },
                    separatorBuilder: (BuildContext context, int index) {
                      return const Gap(8);
                    },
                  ),
            const Gap(8),
          ],
        ),
      ),
    );
  }
}
