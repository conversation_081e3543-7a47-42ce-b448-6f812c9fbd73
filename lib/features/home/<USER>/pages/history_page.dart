import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/di/service_locator.dart';
import '../blocs/history/history_cubit.dart';
import '../../data/models/history_response.dart';

@RoutePage()
class HistoryPage extends StatefulWidget {
  const HistoryPage({super.key});

  @override
  State<HistoryPage> createState() => _HistoryPageState();
}

class _HistoryPageState extends State<HistoryPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late String userId;
  late String authToken;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn,
      ),
    );
    _animationController.forward();
  }

  Future<void> _initializeAndFetch(HistoryCubit cubit) async {
    try {
      final dataManager = sl<DataManager>();
      userId = await dataManager.getUserId() ?? '0';
      authToken = await dataManager.getAuthToken() ?? '0';

      cubit.fetchHistory(
        token: authToken,
        userId: userId,
      );
    } catch (e) {
      // Handle initialization error silently or through cubit's error state
    }
  }

  Future<void> _initializeData() async {
    try {
      final dataManager = sl<DataManager>();
      userId = await dataManager.getUserId() ?? '0';
      authToken = await dataManager.getAuthToken() ?? '0';
    } catch (e) {
      // Handle initialization error
    }
  }

  Future<void> _refreshHistoryData() async {
    try {
      await _initializeData(); // Ensure we have latest auth tokens
      if (mounted) {
        await context.read<HistoryCubit>().fetchHistory(
              token: authToken,
              userId: userId,
            );
      }
      return;
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to refresh history: ${e.toString()}',
        );
      }
      rethrow;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) {
        final cubit = sl<HistoryCubit>();
        // Initialize auth data and fetch history
        _initializeAndFetch(cubit);
        return cubit;
      },
      child: Scaffold(
        backgroundColor: AppColors.lightGrey2.withOpacity(0.9),
        appBar: CustomAppBar(
          title: 'History',
          onBackPressed: () => context.router.back(),
        ),
        body: FadeTransition(
          opacity: _fadeAnimation,
          child: BlocConsumer<HistoryCubit, HistoryState>(
            listener: (context, state) {
              if (state is HistoryError) {
                SnackBarService.error(
                  context: context,
                  message: state.message,
                );
              }
            },
            builder: (context, state) {
              return RefreshIndicator(
                onRefresh: _refreshHistoryData,
                color: AppColors.primaryBlue,
                backgroundColor: Colors.white,
                child: _buildContent(state),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildContent(HistoryState state) {
    if (state is HistoryLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: AppColors.primaryBlue,
        ),
      );
    } else if (state is HistoryError) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: AppColors.blackTint1,
              ),
              const SizedBox(height: 16),
              Text(
                'Error',
                style: Theme.of(context).textTheme.montserratTitleSmall,
              ),
              const SizedBox(height: 8),
              Text(
                state.message,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.montserratParagraphSmall,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: _refreshHistoryData,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryBlue,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    } else if (state is HistoryLoaded) {
      return _buildHistoryList(state.response.data?.history ?? []);
    }

    return const Center(
      child: CircularProgressIndicator(
        color: AppColors.primaryBlue,
      ),
    );
  }

  Widget _buildHistoryList(List<HistoryItem> historyItems) {
    if (historyItems.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.history,
                size: 64,
                color: AppColors.blackTint1,
              ),
              const SizedBox(height: 16),
              Text(
                'No History',
                style: Theme.of(context).textTheme.montserratTitleSmall,
              ),
              const SizedBox(height: 8),
              Text(
                'There are currently no history items available.',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.montserratParagraphSmall,
              ),
            ],
          ),
        ),
      );
    }

    return ListView.separated(
      padding: const EdgeInsets.symmetric(vertical: 0),
      itemCount: historyItems.length,
      separatorBuilder: (context, index) => const Divider(
        height: 1,
        color: AppColors.lightGrey2,
      ),
      itemBuilder: (context, index) {
        final historyItem = historyItems[index];
        return _buildHistoryItem(historyItem);
      },
    );
  }

  Widget _buildHistoryItem(HistoryItem historyItem) {
    return Container(
      color: Colors.white,
      constraints: const BoxConstraints(minHeight: 85),
      child: InkWell(
        onTap: () {
          // Handle history item tap
        },
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              // Main content area
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Schedule date and unaccepted reason
                    Row(
                      children: [
                        Text(
                          _formatDate(historyItem.scheduledDate ?? ''),
                          style: Theme.of(context)
                              .textTheme
                              .montserratParagraphSmall,
                        ),
                        const SizedBox(width: 5),
                        Expanded(
                          child: Text(
                            historyItem.unacceptedReason ?? '',
                            style: Theme.of(context)
                                .textTheme
                                .montserratParagraphSmall,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),

                    // Client row
                    Row(
                      children: [
                        SizedBox(
                          width: 80,
                          child: Text(
                            'Client:',
                            style: Theme.of(context)
                                .textTheme
                                .montserratParagraphSmall
                                .copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ),
                        const SizedBox(width: 5),
                        Expanded(
                          child: Text(
                            historyItem.clientName ?? '',
                            style: Theme.of(context)
                                .textTheme
                                .montserratParagraphSmall,
                          ),
                        ),
                      ],
                    ),

                    // Store row
                    Row(
                      children: [
                        SizedBox(
                          width: 80,
                          child: Text(
                            'Store:',
                            style: Theme.of(context)
                                .textTheme
                                .montserratParagraphSmall
                                .copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ),
                        const SizedBox(width: 5),
                        Expanded(
                          child: Text(
                            historyItem.storeName ?? '',
                            style: Theme.of(context)
                                .textTheme
                                .montserratParagraphSmall,
                          ),
                        ),
                      ],
                    ),

                    // Cycle row
                    Row(
                      children: [
                        SizedBox(
                          width: 80,
                          child: Text(
                            'Cycle:',
                            style: Theme.of(context)
                                .textTheme
                                .montserratParagraphSmall
                                .copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ),
                        const SizedBox(width: 5),
                        Expanded(
                          child: Text(
                            historyItem.cycle ?? '',
                            style: Theme.of(context)
                                .textTheme
                                .montserratParagraphSmall,
                          ),
                        ),
                      ],
                    ),

                    // Budget, Forms, Recorded, Photos row
                    Row(
                      children: [
                        // Budget column
                        SizedBox(
                          width: 80,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Budget:',
                                style: Theme.of(context)
                                    .textTheme
                                    .montserratParagraphSmall
                                    .copyWith(
                                      fontWeight: FontWeight.bold,
                                    ),
                              ),
                              Text(
                                '\$${historyItem.budget ?? 0}',
                                style: Theme.of(context)
                                    .textTheme
                                    .montserratParagraphSmall,
                              ),
                            ],
                          ),
                        ),

                        // Forms column
                        SizedBox(
                          width: 80,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Forms:',
                                style: Theme.of(context)
                                    .textTheme
                                    .montserratParagraphSmall
                                    .copyWith(
                                      fontWeight: FontWeight.bold,
                                    ),
                              ),
                              Text(
                                '${historyItem.forms ?? 0}',
                                style: Theme.of(context)
                                    .textTheme
                                    .montserratParagraphSmall,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    // Recorded and Photos row
                    Row(
                      children: [
                        // Recorded column
                        SizedBox(
                          width: 80,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Minutes:',
                                style: Theme.of(context)
                                    .textTheme
                                    .montserratParagraphSmall
                                    .copyWith(
                                      fontWeight: FontWeight.bold,
                                    ),
                              ),
                              Text(
                                '${historyItem.minutes ?? 0}',
                                style: Theme.of(context)
                                    .textTheme
                                    .montserratParagraphSmall,
                              ),
                            ],
                          ),
                        ),

                        // Photos column
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Photos:',
                                style: Theme.of(context)
                                    .textTheme
                                    .montserratParagraphSmall
                                    .copyWith(
                                      fontWeight: FontWeight.bold,
                                    ),
                              ),
                              Text(
                                '${historyItem.photos ?? 0}',
                                style: Theme.of(context)
                                    .textTheme
                                    .montserratParagraphSmall,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Disclosure indicator (initially hidden as per XML)
              const SizedBox(width: 10),
              const Visibility(
                visible: false, // Initially gone as per XML
                child: Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: AppColors.blackTint1,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDate(String dateString) {
    try {
      if (dateString.isEmpty) return '';
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }
}
