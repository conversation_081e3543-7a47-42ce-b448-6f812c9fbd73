import 'package:flutter/material.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/config/themes/app_fonts.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/leave/leave_cubit.dart';
import 'package:storetrack_app/shared/widgets/confirm_dialog.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import '../../data/models/leave_response.dart';
import '../../../profile/domain/usecases/get_profile_usecase.dart';

@RoutePage()
class LeavePage extends StatelessWidget {
  const LeavePage({super.key});

  Widget _buildLeaveItem(Leave leave, VoidCallback onTap, bool isDeleting) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        title: Text(
          leave.leaveTitle ?? 'Leave Request',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            fontFamily: AppFonts.montserrat,
            color: AppColors.black,
          ),
        ),
        subtitle: leave.leaveDate != null
            ? Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  leave.leaveDate!,
                  style: const TextStyle(
                    fontFamily: AppFonts.montserrat,
                    color: AppColors.blackTint1,
                    fontSize: 14,
                  ),
                ),
              )
            : null,
        trailing: Icon(
          Icons.check_circle,
          color: (leave.isSelected ?? false)
              ? AppColors.primaryBlue
              : AppColors.black20,
          size: 24,
        ),
        onTap: isDeleting ? null : onTap,
      ),
    );
  }

  void _showNotAllowedDialog(BuildContext context) {
    ConfirmDialog.show(
      context: context,
      title: 'Not allowed',
      message: 'Please contact your manager.',
      confirmText: 'OK',
      onConfirm: () {
        Navigator.of(context).pop();
      },
    );
  }

  Future<bool> _checkOrganizationAccess(BuildContext context) async {
    try {
      final dataManager = sl<DataManager>();
      final token = await dataManager.getAuthToken();
      final userId = await dataManager.getUserId();

      if (token != null && userId != null) {
        final profileUseCase = sl<GetProfileUseCase>();
        final result = await profileUseCase.call(
          token: token,
          userId: userId,
        );

        if (result.isSuccess && result.data?.data?.orgIDs != null) {
          final orgIDs = result.data!.data!.orgIDs!;
          // Check if user belongs to organization ID "8"
          if (orgIDs.contains("8")) {
            return false; // Not allowed
          }
        }
      }
      return true; // Allowed
    } catch (e) {
      return true; // Allow by default in case of error
    }
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<bool>(
      future: _checkOrganizationAccess(context),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Scaffold(
            backgroundColor: const Color(0xFFF5F5F5), // Ivory white background
            appBar: CustomAppBar(
              title: 'Leave',
              onBackPressed: () => context.router.back(),
            ),
            body: const Center(
              child: CircularProgressIndicator(
                color: AppColors.primaryBlue,
              ),
            ),
          );
        }

        final isAllowed = snapshot.data ?? true;

        if (!isAllowed) {
          // Show dialog and return immediately
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _showNotAllowedDialog(context);
            Navigator.of(context).pop();
          });
          return const SizedBox.shrink();
        }

        return BlocProvider(
          create: (_) => sl<LeaveCubit>()..fetchLeaves(),
          child: BlocConsumer<LeaveCubit, LeaveState>(
            listener: (context, state) {
              if (state is LeaveError) {
                SnackBarService.error(
                  context: context,
                  message: state.message,
                );
              } else if (state is LeaveDeleted) {
                SnackBarService.success(
                  context: context,
                  message: 'Selected leave requests deleted successfully',
                );
              }
            },
            builder: (context, state) {
              return Scaffold(
                backgroundColor:
                    const Color(0xFFF5F5F5), // Ivory white background
                appBar: CustomAppBar(
                  title: 'Leave',
                  onBackPressed: () => context.router.back(),
                ),
                body: _buildBody(state, context),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildBody(LeaveState state, BuildContext context) {
    if (state is LeaveLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: AppColors.primaryBlue,
        ),
      );
    } else if (state is LeaveLoaded) {
      final leaves = state.response.data?.leaves ?? [];
      return Column(
        children: [
          Expanded(
            child: leaves.isEmpty
                ? const Center(
                    child: Text(
                      'No leave requests found',
                      style: TextStyle(
                        fontFamily: AppFonts.montserrat,
                        color: AppColors.blackTint1,
                        fontSize: 16,
                      ),
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    itemCount: leaves.length,
                    itemBuilder: (context, index) {
                      final leave = leaves[index];
                      return _buildLeaveItem(
                        leave,
                        () {
                          context
                              .read<LeaveCubit>()
                              .toggleLeaveSelection(leave.leaveId);
                        },
                        state is LeaveDeleting,
                      );
                    },
                  ),
          ),
          // Delete button at the bottom
          if (state.hasSelectedItems)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              child: ElevatedButton(
                onPressed: state is LeaveDeleting
                    ? null
                    : () {
                        context.read<LeaveCubit>().deleteSelectedLeaves();
                      },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: state is LeaveDeleting
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.white,
                        ),
                      )
                    : const Text(
                        'Delete Selected',
                        style: TextStyle(
                          fontFamily: AppFonts.montserrat,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
              ),
            ),
        ],
      );
    } else if (state is LeaveError) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: AppColors.blackTint1,
              ),
              const SizedBox(height: 16),
              Text(
                'Error',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontFamily: AppFonts.montserrat,
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 8),
              Text(
                state.message,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontFamily: AppFonts.montserrat,
                  color: AppColors.blackTint1,
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  context.read<LeaveCubit>().fetchLeaves();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryBlue,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }
    return const SizedBox.shrink();
  }
}
