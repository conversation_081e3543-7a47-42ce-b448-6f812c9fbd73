import 'package:bloc/bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:storetrack_app/core/services/location_service.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/home/<USER>/entities/open_count_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/open_tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_open_count_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_tasks_usecase.dart';
import 'package:storetrack_app/shared/models/result.dart';

import 'dashboard_state.dart';

class DashboardCubit extends Cubit<DashboardState> {
  final GetTasksUseCase _unscheduleTaskUseCase;
  final GetOpenCountUseCase _getOpenCountUseCase;

  // Using GetIt to access these services
  final LocationService _locationService = GetIt.instance<LocationService>();
  final DataManager _dataManager = GetIt.instance<DataManager>();

  DashboardCubit(
    this._unscheduleTaskUseCase,
    this._getOpenCountUseCase,
  ) : super(DashboardInitial());

  Future<void> fetchDashboardData(TasksRequestEntity request) async {
    emit(DashboardLoading());

    try {
      // Get current location for open count API
      final position = await _locationService.getCurrentPosition();

      // If we have location, prepare to call both usecases simultaneously
      if (position != null) {
        final userId = await _dataManager.getUserId();
        final token = await _dataManager.getAuthToken();

        if (userId != null && token != null) {
          // Create request for open count
          final openCountRequest = OpenCountRequestEntity(
            latitude: position.latitude!,
            longitude: position.longitude!,
            userId: userId,
            token: token,
          );

          // Call both usecases simultaneously using Future.wait
          final results = await Future.wait([
            _unscheduleTaskUseCase(request),
            _getOpenCountUseCase(openCountRequest),
          ]);

          final operationNames = ['Tasks', 'OpenCount'];

          if (results.hasFailures) {
            final errorMessage =
                results.getCombinedErrorMessage(operationNames);
            emit(DashboardError(errorMessage));
            return;
          }

          // All succeeded, extract data
          final tasksResponse = results[0].data as TasksResponseEntity;
          final openTaskResponse = results[1].data as OpenTaskResponseEntity;
          final counts = _calculateCounts(tasksResponse);

          emit(DashboardLoaded(
            response: tasksResponse,
            countUnscheduled: counts['unscheduled'] ?? 0,
            countScheduled: counts['scheduled'] ?? 0,
            countPos: counts['pos'] ?? 0,
            countCompleted: counts['completed'] ?? 0,
            countToday: counts['today'] ?? 0,
            openTaskResponse: openTaskResponse,
            countTaken: openTaskResponse.data.countTaken,
            countAvailable: openTaskResponse.data.countAvailable,
            countAutoschedule: openTaskResponse.data.countAutoschedule,
            otShow: openTaskResponse.data.otShow,
          ));
          return;
        }
      }

      // If location or user data not available, just fetch tasks data
      logger(
          'Location or user data not available, just fetch tasks data qwe123');
      final tasksResult = await _unscheduleTaskUseCase(request);

      if (!tasksResult.isSuccess) {
        final errorMessage = tasksResult.error?.toString() ??
            'Unknown error occurred while fetching dashboard data.';
        emit(DashboardError(errorMessage));
        return;
      }

      final tasksResponse = tasksResult.data as TasksResponseEntity;
      final counts = _calculateCounts(tasksResponse);

      emit(DashboardLoaded(
        response: tasksResponse,
        countUnscheduled: counts['unscheduled'] ?? 0,
        countScheduled: counts['scheduled'] ?? 0,
        countPos: counts['pos'] ?? 0,
        countCompleted: counts['completed'] ?? 0,
        countToday: counts['today'] ?? 0,
      ));
    } catch (e) {
      emit(DashboardError('An unexpected error occurred: ${e.toString()}'));
    }
  }

  Map<String, int> _calculateCounts(TasksResponseEntity response) {
    // Initialize counts
    int countUnscheduled = 0;
    int countScheduled = 0;
    int countCompleted = 0;
    int countToday = 0;
    int countPos = 0;

    // Get today's date at midnight for comparison
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    // Extract all tasks from the response
    List<TaskDetail> allTasks = response.data?["add_tasks"] ?? [];

    for (var task in allTasks) {
      // Count unscheduled tasks
      if (task.taskStatus == "Tentative" &&
          task.taskId != 0 &&
          task.isOpen == false) {
        countUnscheduled++;
      }

      // Count scheduled tasks
      if (task.taskStatus == "Confirmed" && task.isOpen == false) {
        countScheduled++;

        // Check if task is scheduled for today
        final scheduledDate = task.scheduledTimeStamp;
        if (scheduledDate != null) {
          final taskDate = DateTime(
              scheduledDate.year, scheduledDate.month, scheduledDate.day);
          if (taskDate.isAtSameMomentAs(today)) {
            countToday++;
          }
        }
      }

      // Count completed tasks
      if ((task.taskStatus == "Successful" ||
          task.taskStatus == "Unsuccessful")) {
        countCompleted++;
      }

      // Count POS tasks
      if (task.posRequired == true) {
        countPos++;
      }
    }

    return {
      'unscheduled': countUnscheduled,
      'scheduled': countScheduled,
      'completed': countCompleted,
      'today': countToday,
      'pos': countPos,
    };
  }

  void resetState() {
    emit(DashboardInitial());
  }
}
