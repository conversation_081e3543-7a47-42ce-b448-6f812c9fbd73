import 'package:bloc/bloc.dart';
import 'package:storetrack_app/features/home/<USER>/entities/calendar_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/submit_report_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/submit_report_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_calendar_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/submit_report_usecase.dart';
import 'package:storetrack_app/shared/models/result.dart';

import '../../../domain/entities/tasks_request_entity.dart';
import '../../../domain/entities/tasks_response_entity.dart';
import '../../../domain/usecases/get_tasks_usecase.dart';
import 'unschedule_state.dart';

class UnscheduleTaskCubit extends Cubit<UnscheduleTaskState> {
  final GetTasksUseCase _getTasksUseCase;
  final GetCalendarUseCase _getCalendarUseCase;
  final SubmitReportUseCase _submitReportUseCase;

  UnscheduleTaskCubit(
    this._getTasksUseCase,
    this._getCalendarUseCase,
    this._submitReportUseCase,
  ) : super(UnscheduleTaskInitial());

  Future<void> getData(TasksRequestEntity request) async {
    emit(UnscheduleTaskLoading());

    final results = await Future.wait([
      _getTasksUseCase(request),
      _getCalendarUseCase(GetCalendarParams(
        token: request.token,
        userId: request.userId,
      )),
    ]);

    final operationNames = ['Tasks', 'Calendar'];

    if (results.hasFailures) {
      final errorMessage = results.getCombinedErrorMessage(operationNames);
      emit(UnscheduleTaskError(errorMessage));
      return;
    }

    // All succeeded, extract data
    emit(UnscheduleTaskSuccess(
      tasksResponse: results[0].data as TasksResponseEntity,
      calendarResponse: results[1].data as CalendarResponseEntity,
    ));
  }

  void resetState() {
    emit(UnscheduleTaskInitial());
  }

  // Submit report for scheduling tasks
  Future<void> submitReport(SubmitReportRequestEntity request) async {
    emit(UnscheduleTaskLoading()); // Signal start of operation

    final Result<SubmitReportResponseEntity> result =
        await _submitReportUseCase.call(request);

    if (result.isSuccess) {
      // After successful submission, refresh the task list
      final tasksRequest = TasksRequestEntity(
        deviceUid: request.deviceUid ?? "",
        userId: request.userId ?? "",
        appversion: request.appversion ?? "",
        tasks: const [],
        token: request.token ?? "",
      );

      final calendarParams = GetCalendarParams(
        token: request.token ?? "",
        userId: request.userId ?? "",
      );

      final refreshResults = await Future.wait([
        _getTasksUseCase(tasksRequest),
        _getCalendarUseCase(calendarParams),
      ]);

      final refreshOperationNames = ['Tasks', 'Calendar'];

      if (refreshResults.hasFailures) {
        final errorMessage =
            refreshResults.getCombinedErrorMessage(refreshOperationNames);
        emit(UnscheduleTaskError(errorMessage));
        return;
      }

      emit(UnscheduleTaskSuccess(
        tasksResponse: refreshResults[0].data as TasksResponseEntity,
        calendarResponse: refreshResults[1].data as CalendarResponseEntity,
      ));
    } else {
      // Failure: Extract error message from Result
      final errorMessage = result.error?.toString() ??
          'Unknown error occurred while submitting report.';
      emit(UnscheduleTaskError(errorMessage));
    }
  }
}
