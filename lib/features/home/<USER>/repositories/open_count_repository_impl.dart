import 'package:storetrack_app/core/network/network_info.dart';
import 'package:storetrack_app/features/home/<USER>/datasources/home_local_datasource.dart';
import 'package:storetrack_app/features/home/<USER>/datasources/home_remote_datasource.dart';
import 'package:storetrack_app/features/home/<USER>/entities/open_count_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/open_tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/repositories/open_count_repository.dart';
import 'package:storetrack_app/shared/models/result.dart';

class OpenCountRepositoryImpl implements OpenCountRepository {
  final HomeRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;
  final HomeLocalDataSource localDataSource;

  OpenCountRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
    required this.localDataSource,
  });

  @override
  Future<Result<OpenTaskResponseEntity>> getOpenCount(
      OpenCountRequestEntity request) async {
    if (await networkInfo.isConnected) {
      // Online: fetch from remote
      return await remoteDataSource.getOpenCount(request);
    } else {
      // Offline: return failure
      return Result.failure(
          'No internet connection. Open count data requires an active connection.');
    }
  }
}
