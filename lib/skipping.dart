import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

skipping() {
  final List<String> skippedQuestionsConditions = [];
  final List<String> allQuestionAnswerIDs = [];
  final List<SkippedQuestionIDs> skippedQPMeasurementIDs = [];

  var task = TaskDetail();
  var formModel = task.forms![0];

  final formAnswers = formModel.questionAnswers;
  if (formAnswers != null && formAnswers.isNotEmpty) {
    for (final answer in formAnswers) {
      final questionModel = formModel.questions
          ?.firstWhere((q) => q.questionId == answer.questionId);
      allQuestionAnswerIDs.add(answer.questionId?.toString() ?? '');

      if (questionModel != null) {
        if (questionModel.isComment == false) {
          if (questionModel.measurements != null) {
            for (final measurement in questionModel.measurements!) {
              if (answer.measurementId == measurement.measurementId) {
                if (answer.measurementOptionId != null) {
                  final isInvisible = answer.measurementOptionId == -2 &&
                      answer.measurementTextResult == '-' &&
                      answer.measurementOptionIds != null;

                  if (isInvisible) {
                    final skippedID = SkippedQuestionIDs(
                      answer.questionId?.toString() ?? '',
                      answer.questionpartId?.toString() ?? '',
                      answer.measurementId?.toString() ?? '',
                    );

                    if (!skippedQPMeasurementIDs.contains(skippedID)) {
                      skippedQPMeasurementIDs.add(skippedID);
                    }
                  }
                }
              }
            }
          }
        } else {
          final isCommentInvisible = answer.commentTypeId == -2 &&
              answer.measurementTextResult == '-' &&
              answer.measurementOptionIds != null;

          if (isCommentInvisible &&
              !skippedQuestionsConditions
                  .contains(questionModel.questionId?.toString() ?? '')) {
            skippedQuestionsConditions
                .add(questionModel.questionId?.toString() ?? '');
          }
        }
      }
    }
  }

  // Post-processing skipped measurement-based questions
  for (final qModel in formModel.questions!) {
    int count = 0;

    if (qModel.isComment == false) {
      for (final skippedID in skippedQPMeasurementIDs) {
        if (skippedID.questionID == qModel.questionId?.toString()) {
          count++;
        }
      }

      final totalExpected = (qModel.measurements?.length ?? 0) *
          (qModel.questionParts?.length ?? 0);

      if (count == totalExpected &&
          !skippedQuestionsConditions
              .contains(qModel.questionId?.toString() ?? '')) {
        skippedQuestionsConditions.add(qModel.questionId?.toString() ?? '');
      }
    }

    // Evaluate question conditions (e.g., disappear logic)
    if (qModel.questionConditions != null) {
      for (final condition in qModel.questionConditions!) {
        final targetID = condition.actionQuestionId?.toString() ?? '';
        final targetAction = condition.action;

        if (targetAction == 'disappear' &&
            !skippedQuestionsConditions.contains(targetID) &&
            !allQuestionAnswerIDs.contains(targetID)) {
          skippedQuestionsConditions.add(targetID);
        }
      }
    }
  }

  for (final s in skippedQuestionsConditions) {
    logger("Skipped Question ID: $s");
  }

  // Persist skipped question IDs
  var skippingList = skippedQuestionsConditions;
  return skippingList;
}

class SkippedQuestionIDs {
  String questionID;
  String questionPartID;
  String measurementID;

  SkippedQuestionIDs(this.questionID, this.questionPartID, this.measurementID);
}

bool isQuestionSkipped(String questionID, List<String> skippingList) {
  if (skippingList.contains(questionID)) {
    return true;
  } else {
    bool isSupplementaryMulti = false;

    isSupplementaryMulti = isSupplementaryMultiQuestion(Question());
    if (isSupplementaryMulti) {
      return !isSupplementaryMultiQuestionPositiveAnswered(Question());
    }
    return false;
  }
}

bool isSupplementaryMultiQuestion(Question questionModel) {
  return questionModel.multiMeasurementId != null &&
      questionModel.multiMeasurementId.toString() != "0" &&
      questionModel.multiMeasurementId.toString().isNotEmpty;
}

isSupplementaryMultiQuestionPositiveAnswered(Question questionModel) {
  var isSupplementMultiAnswered = false;

  // Check if the question answer exists and has a positive result
  final questionAnswer = Form().questionAnswers?.firstWhere(
        (qa) => qa.measurementId == questionModel.multiMeasurementId,
        orElse: () => QuestionAnswer(),
      );

  if (questionAnswer?.measurementTextResult != null &&
      questionAnswer?.measurementTextResult != '-' &&
      int.tryParse(questionAnswer!.measurementTextResult ?? '') != null) {
    final supplementaryAnswer =
        int.parse(questionAnswer.measurementTextResult!);
    if (supplementaryAnswer > 0) {
      isSupplementMultiAnswered = true;
    }
  }

  return isSupplementMultiAnswered;
}

getSupplementaryMultiAnswer(Question questionModel) {
  var supplementaryAnswer = 0;

  // Check if the question answer exists and has a positive result
  final questionAnswer = Form().questionAnswers?.firstWhere(
        (qa) => qa.measurementId == questionModel.multiMeasurementId,
        orElse: () => QuestionAnswer(),
      );

  if (questionAnswer?.measurementTextResult != null &&
      questionAnswer?.measurementTextResult != '-' &&
      int.tryParse(questionAnswer!.measurementTextResult ?? '') != null) {
    supplementaryAnswer = int.parse(questionAnswer.measurementTextResult!);
  }

  return supplementaryAnswer;
}

isQuestionAnswered(String questionID, List<String> skippingList) {
  var count = 0;
  var questionModel = Question();
  var questionParts = questionModel.questionParts ?? [];
  for (var part in questionParts) {
    if (questionModel.isMulti == true) {
    } else {
      if (!isQuestionPartMultiAnswered()) {
        count++;
      }
    }
  }
}

isQuestionPartMultiAnswered() {}

isQuestionMandatory() {
  var isMandatory = false;
  var questionModel = Question();
  if (questionModel.isMulti == false && questionModel.isComment == false) {
    for (var measurement in questionModel.measurements!) {
      if (measurement.measurementValidations != null) {
        for (var validation in measurement.measurementValidations!) {
          if (validation.required == true) {
            isMandatory = true;
            break;
          }
        }
      }
    }
  } else {
    if (questionModel.isMulti == true) {
      bool isSupplementaryQuestion =
          isSupplementaryMultiQuestion(questionModel);
    }
  }
}

checkQuestionPartMultipartExpanded() {
  int oneOption = 0;
  var questionModel = Question();
  for (var questionPart in questionModel.questionParts!) {
    // if (questionPart) {
    //   oneOption++;
    // }
  }
}
